/*!
* Tabler v1.0.0-beta19 (https://tabler.io)
* @version 1.0.0-beta19
* @link https://tabler.io
* Copyright 2018-2023 The Tabler Authors
* Copyright 2018-2023 codecalm.net Paweł Kuna
* Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
*/
var e=new Map;function t(t){var s=e.get(t);s&&s.destroy()}function o(t){var s=e.get(t);s&&s.update()}var r=null;"undefined"==typeof window?((r=function(e){return e}).destroy=function(e){return e},r.update=function(e){return e}):((r=function(t,s){return t&&Array.prototype.forEach.call(t.length?t:[t],(function(t){return function(t){if(t&&t.nodeName&&"TEXTAREA"===t.nodeName&&!e.has(t)){var s,i=null,n=window.getComputedStyle(t),r=(s=t.value,function(){o({testForHeightReduction:""===s||!t.value.startsWith(s),restoreTextAlign:null}),s=t.value}),a=function(s){t.removeEventListener("autosize:destroy",a),t.removeEventListener("autosize:update",l),t.removeEventListener("input",r),window.removeEventListener("resize",l),Object.keys(s).forEach((function(e){return t.style[e]=s[e]})),e.delete(t)}.bind(t,{height:t.style.height,resize:t.style.resize,textAlign:t.style.textAlign,overflowY:t.style.overflowY,overflowX:t.style.overflowX,wordWrap:t.style.wordWrap});t.addEventListener("autosize:destroy",a),t.addEventListener("autosize:update",l),t.addEventListener("input",r),window.addEventListener("resize",l),t.style.overflowX="hidden",t.style.wordWrap="break-word",e.set(t,{destroy:a,update:l}),l()}function o(e){var s,r,a=e.restoreTextAlign,l=void 0===a?null:a,u=e.testForHeightReduction,c=void 0===u||u,h=n.overflowY;if(0!==t.scrollHeight&&("vertical"===n.resize?t.style.resize="none":"both"===n.resize&&(t.style.resize="horizontal"),c&&(s=function(e){for(var t=[];e&&e.parentNode&&e.parentNode instanceof Element;)e.parentNode.scrollTop&&t.push([e.parentNode,e.parentNode.scrollTop]),e=e.parentNode;return function(){return t.forEach((function(e){var t=e[0],s=e[1];t.style.scrollBehavior="auto",t.scrollTop=s,t.style.scrollBehavior=null}))}}(t),t.style.height=""),r="content-box"===n.boxSizing?t.scrollHeight-(parseFloat(n.paddingTop)+parseFloat(n.paddingBottom)):t.scrollHeight+parseFloat(n.borderTopWidth)+parseFloat(n.borderBottomWidth),"none"!==n.maxHeight&&r>parseFloat(n.maxHeight)?("hidden"===n.overflowY&&(t.style.overflow="scroll"),r=parseFloat(n.maxHeight)):"hidden"!==n.overflowY&&(t.style.overflow="hidden"),t.style.height=r+"px",l&&(t.style.textAlign=l),s&&s(),i!==r&&(t.dispatchEvent(new Event("autosize:resized",{bubbles:!0})),i=r),h!==n.overflow&&!l)){var d=n.textAlign;"hidden"===n.overflow&&(t.style.textAlign="start"===d?"end":"start"),o({restoreTextAlign:d,testForHeightReduction:!0})}}function l(){o({testForHeightReduction:!0,restoreTextAlign:null})}}(t)})),t}).destroy=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],t),e},r.update=function(e){return e&&Array.prototype.forEach.call(e.length?e:[e],o),e});var n=r,elements=document.querySelectorAll('[data-bs-toggle="autosize"]');function _objectWithoutPropertiesLoose(e,t){if(null==e)return{};var s,i,n={},r=Object.keys(e);for(i=0;i<r.length;i++)s=r[i],t.indexOf(s)>=0||(n[s]=e[s]);return n}function IMask(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new IMask.InputMask(e,t)}elements.length&&elements.forEach((function(e){n(e)}));class ChangeDetails{constructor(e){Object.assign(this,{inserted:"",rawInserted:"",skip:!1,tailShift:0},e)}aggregate(e){return this.rawInserted+=e.rawInserted,this.skip=this.skip||e.skip,this.inserted+=e.inserted,this.tailShift+=e.tailShift,this}get offset(){return this.tailShift+this.inserted.length}}function isString(e){return"string"==typeof e||e instanceof String}IMask.ChangeDetails=ChangeDetails;const DIRECTION={NONE:"NONE",LEFT:"LEFT",FORCE_LEFT:"FORCE_LEFT",RIGHT:"RIGHT",FORCE_RIGHT:"FORCE_RIGHT"};function forceDirection(e){switch(e){case DIRECTION.LEFT:return DIRECTION.FORCE_LEFT;case DIRECTION.RIGHT:return DIRECTION.FORCE_RIGHT;default:return e}}function escapeRegExp(e){return e.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function normalizePrepare(e){return Array.isArray(e)?e:[e,new ChangeDetails]}function objectIncludes(e,t){if(t===e)return!0;var s,i=Array.isArray(t),n=Array.isArray(e);if(i&&n){if(t.length!=e.length)return!1;for(s=0;s<t.length;s++)if(!objectIncludes(t[s],e[s]))return!1;return!0}if(i!=n)return!1;if(t&&e&&"object"==typeof t&&"object"==typeof e){var r=t instanceof Date,a=e instanceof Date;if(r&&a)return t.getTime()==e.getTime();if(r!=a)return!1;var o=t instanceof RegExp,l=e instanceof RegExp;if(o&&l)return t.toString()==e.toString();if(o!=l)return!1;var u=Object.keys(t);for(s=0;s<u.length;s++)if(!Object.prototype.hasOwnProperty.call(e,u[s]))return!1;for(s=0;s<u.length;s++)if(!objectIncludes(e[u[s]],t[u[s]]))return!1;return!0}return!(!t||!e||"function"!=typeof t||"function"!=typeof e)&&t.toString()===e.toString()}class ActionDetails{constructor(e,t,s,i){for(this.value=e,this.cursorPos=t,this.oldValue=s,this.oldSelection=i;this.value.slice(0,this.startChangePos)!==this.oldValue.slice(0,this.startChangePos);)--this.oldSelection.start}get startChangePos(){return Math.min(this.cursorPos,this.oldSelection.start)}get insertedCount(){return this.cursorPos-this.startChangePos}get inserted(){return this.value.substr(this.startChangePos,this.insertedCount)}get removedCount(){return Math.max(this.oldSelection.end-this.startChangePos||this.oldValue.length-this.value.length,0)}get removed(){return this.oldValue.substr(this.startChangePos,this.removedCount)}get head(){return this.value.substring(0,this.startChangePos)}get tail(){return this.value.substring(this.startChangePos+this.insertedCount)}get removeDirection(){return!this.removedCount||this.insertedCount?DIRECTION.NONE:this.oldSelection.end!==this.cursorPos&&this.oldSelection.start!==this.cursorPos||this.oldSelection.end!==this.oldSelection.start?DIRECTION.LEFT:DIRECTION.RIGHT}}class ContinuousTailDetails{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2?arguments[2]:void 0;this.value=e,this.from=t,this.stop=s}toString(){return this.value}extend(e){this.value+=String(e)}appendTo(e){return e.append(this.toString(),{tail:!0}).aggregate(e._appendPlaceholder())}get state(){return{value:this.value,from:this.from,stop:this.stop}}set state(e){Object.assign(this,e)}unshift(e){if(!this.value.length||null!=e&&this.from>=e)return"";const t=this.value[0];return this.value=this.value.slice(1),t}shift(){if(!this.value.length)return"";const e=this.value[this.value.length-1];return this.value=this.value.slice(0,-1),e}}class Masked{constructor(e){this._value="",this._update(Object.assign({},Masked.DEFAULTS,e)),this.isInitialized=!0}updateOptions(e){Object.keys(e).length&&this.withValueRefresh(this._update.bind(this,e))}_update(e){Object.assign(this,e)}get state(){return{_value:this.value}}set state(e){this._value=e._value}reset(){this._value=""}get value(){return this._value}set value(e){this.resolve(e)}resolve(e){return this.reset(),this.append(e,{input:!0},""),this.doCommit(),this.value}get unmaskedValue(){return this.value}set unmaskedValue(e){this.reset(),this.append(e,{},""),this.doCommit()}get typedValue(){return this.doParse(this.value)}set typedValue(e){this.value=this.doFormat(e)}get rawInputValue(){return this.extractInput(0,this.value.length,{raw:!0})}set rawInputValue(e){this.reset(),this.append(e,{raw:!0},""),this.doCommit()}get displayValue(){return this.value}get isComplete(){return!0}get isFilled(){return this.isComplete}nearestInputPos(e,t){return e}totalInputPositions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return Math.min(this.value.length,t-e)}extractInput(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return this.value.slice(e,t)}extractTail(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return new ContinuousTailDetails(this.extractInput(e,t),e)}appendTail(e){return isString(e)&&(e=new ContinuousTailDetails(String(e))),e.appendTo(this)}_appendCharRaw(e){return e?(this._value+=e,new ChangeDetails({inserted:e,rawInserted:e})):new ChangeDetails}_appendChar(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;const i=this.state;let n;if([e,n]=normalizePrepare(this.doPrepare(e,t)),n=n.aggregate(this._appendCharRaw(e,t)),n.inserted){let e,r=!1!==this.doValidate(t);if(r&&null!=s){const t=this.state;!0===this.overwrite&&(e=s.state,s.unshift(this.value.length-n.tailShift));let i=this.appendTail(s);r=i.rawInserted===s.toString(),r&&i.inserted||"shift"!==this.overwrite||(this.state=t,e=s.state,s.shift(),i=this.appendTail(s),r=i.rawInserted===s.toString()),r&&i.inserted&&(this.state=t)}r||(n=new ChangeDetails,this.state=i,s&&e&&(s.state=e))}return n}_appendPlaceholder(){return new ChangeDetails}_appendEager(){return new ChangeDetails}append(e,t,s){if(!isString(e))throw new Error("value should be string");const i=new ChangeDetails,n=isString(s)?new ContinuousTailDetails(String(s)):s;null!=t&&t.tail&&(t._beforeTailState=this.state);for(let s=0;s<e.length;++s){const r=this._appendChar(e[s],t,n);if(!r.rawInserted&&!this.doSkipInvalid(e[s],t,n))break;i.aggregate(r)}return null!=n&&(i.tailShift+=this.appendTail(n).tailShift),(!0===this.eager||"append"===this.eager)&&null!=t&&t.input&&e&&i.aggregate(this._appendEager()),i}remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return this._value=this.value.slice(0,e)+this.value.slice(t),new ChangeDetails}withValueRefresh(e){if(this._refreshing||!this.isInitialized)return e();this._refreshing=!0;const t=this.rawInputValue,s=this.value,i=e();return this.rawInputValue=t,this.value&&this.value!==s&&0===s.indexOf(this.value)&&this.append(s.slice(this.value.length),{},""),delete this._refreshing,i}runIsolated(e){if(this._isolated||!this.isInitialized)return e(this);this._isolated=!0;const t=this.state,s=e(this);return this.state=t,delete this._isolated,s}doSkipInvalid(e){return this.skipInvalid}doPrepare(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.prepare?this.prepare(e,this,t):e}doValidate(e){return(!this.validate||this.validate(this.value,this,e))&&(!this.parent||this.parent.doValidate(e))}doCommit(){this.commit&&this.commit(this.value,this)}doFormat(e){return this.format?this.format(e,this):e}doParse(e){return this.parse?this.parse(e,this):e}splice(e,t,s,i){let n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{input:!0};const r=e+t,a=this.extractTail(r),o=!0===this.eager||"remove"===this.eager;let l;o&&(i=forceDirection(i),l=this.extractInput(0,r,{raw:!0}));let u=e;const c=new ChangeDetails;if(i!==DIRECTION.NONE&&(u=this.nearestInputPos(e,t>1&&0!==e&&!o?DIRECTION.NONE:i),c.tailShift=u-e),c.aggregate(this.remove(u)),o&&i!==DIRECTION.NONE&&l===this.rawInputValue)if(i===DIRECTION.FORCE_LEFT){let e;for(;l===this.rawInputValue&&(e=this.value.length);)c.aggregate(new ChangeDetails({tailShift:-1})).aggregate(this.remove(e-1))}else i===DIRECTION.FORCE_RIGHT&&a.unshift();return c.aggregate(this.append(s,n,a))}maskEquals(e){return this.mask===e}typedValueEquals(e){const t=this.typedValue;return e===t||Masked.EMPTY_VALUES.includes(e)&&Masked.EMPTY_VALUES.includes(t)||this.doFormat(e)===this.doFormat(this.typedValue)}}function maskedClass(e){if(null==e)throw new Error("mask property should be defined");return e instanceof RegExp?IMask.MaskedRegExp:isString(e)?IMask.MaskedPattern:e instanceof Date||e===Date?IMask.MaskedDate:e instanceof Number||"number"==typeof e||e===Number?IMask.MaskedNumber:Array.isArray(e)||e===Array?IMask.MaskedDynamic:IMask.Masked&&e.prototype instanceof IMask.Masked?e:e instanceof IMask.Masked?e.constructor:e instanceof Function?IMask.MaskedFunction:(console.warn("Mask not found for mask",e),IMask.Masked)}function createMask(e){if(IMask.Masked&&e instanceof IMask.Masked)return e;const t=(e=Object.assign({},e)).mask;if(IMask.Masked&&t instanceof IMask.Masked)return t;const s=maskedClass(t);if(!s)throw new Error("Masked class is not found for provided mask, appropriate module needs to be import manually before creating mask.");return new s(e)}Masked.DEFAULTS={format:String,parse:e=>e,skipInvalid:!0},Masked.EMPTY_VALUES=[void 0,null,""],IMask.Masked=Masked,IMask.createMask=createMask;const _excluded$4=["parent","isOptional","placeholderChar","displayChar","lazy","eager"],DEFAULT_INPUT_DEFINITIONS={0:/\d/,a:/[\u0041-\u005A\u0061-\u007A\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,"*":/./};class PatternInputDefinition{constructor(e){const{parent:t,isOptional:s,placeholderChar:i,displayChar:n,lazy:r,eager:a}=e,o=_objectWithoutPropertiesLoose(e,_excluded$4);this.masked=createMask(o),Object.assign(this,{parent:t,isOptional:s,placeholderChar:i,displayChar:n,lazy:r,eager:a})}reset(){this.isFilled=!1,this.masked.reset()}remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return 0===e&&t>=1?(this.isFilled=!1,this.masked.remove(e,t)):new ChangeDetails}get value(){return this.masked.value||(this.isFilled&&!this.isOptional?this.placeholderChar:"")}get unmaskedValue(){return this.masked.unmaskedValue}get displayValue(){return this.masked.value&&this.displayChar||this.value}get isComplete(){return Boolean(this.masked.value)||this.isOptional}_appendChar(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.isFilled)return new ChangeDetails;const s=this.masked.state,i=this.masked._appendChar(e,t);return i.inserted&&!1===this.doValidate(t)&&(i.inserted=i.rawInserted="",this.masked.state=s),i.inserted||this.isOptional||this.lazy||t.input||(i.inserted=this.placeholderChar),i.skip=!i.inserted&&!this.isOptional,this.isFilled=Boolean(i.inserted),i}append(){return this.masked.append(...arguments)}_appendPlaceholder(){const e=new ChangeDetails;return this.isFilled||this.isOptional||(this.isFilled=!0,e.inserted=this.placeholderChar),e}_appendEager(){return new ChangeDetails}extractTail(){return this.masked.extractTail(...arguments)}appendTail(){return this.masked.appendTail(...arguments)}extractInput(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0;return this.masked.extractInput(e,t,s)}nearestInputPos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:DIRECTION.NONE;const s=this.value.length,i=Math.min(Math.max(e,0),s);switch(t){case DIRECTION.LEFT:case DIRECTION.FORCE_LEFT:return this.isComplete?i:0;case DIRECTION.RIGHT:case DIRECTION.FORCE_RIGHT:return this.isComplete?i:s;default:return i}}totalInputPositions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;return this.value.slice(e,t).length}doValidate(){return this.masked.doValidate(...arguments)&&(!this.parent||this.parent.doValidate(...arguments))}doCommit(){this.masked.doCommit()}get state(){return{masked:this.masked.state,isFilled:this.isFilled}}set state(e){this.masked.state=e.masked,this.isFilled=e.isFilled}}class PatternFixedDefinition{constructor(e){Object.assign(this,e),this._value="",this.isFixed=!0}get value(){return this._value}get unmaskedValue(){return this.isUnmasking?this.value:""}get displayValue(){return this.value}reset(){this._isRawInput=!1,this._value=""}remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._value.length;return this._value=this._value.slice(0,e)+this._value.slice(t),this._value||(this._isRawInput=!1),new ChangeDetails}nearestInputPos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:DIRECTION.NONE;const s=this._value.length;switch(t){case DIRECTION.LEFT:case DIRECTION.FORCE_LEFT:return 0;default:return s}}totalInputPositions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._value.length;return this._isRawInput?t-e:0}extractInput(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._value.length;return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).raw&&this._isRawInput&&this._value.slice(e,t)||""}get isComplete(){return!0}get isFilled(){return Boolean(this._value)}_appendChar(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=new ChangeDetails;if(this.isFilled)return s;const i=!0===this.eager||"append"===this.eager,n=this.char===e&&(this.isUnmasking||t.input||t.raw)&&(!t.raw||!i)&&!t.tail;return n&&(s.rawInserted=this.char),this._value=s.inserted=this.char,this._isRawInput=n&&(t.raw||t.input),s}_appendEager(){return this._appendChar(this.char,{tail:!0})}_appendPlaceholder(){const e=new ChangeDetails;return this.isFilled||(this._value=e.inserted=this.char),e}extractTail(){return arguments.length>1&&void 0!==arguments[1]||this.value.length,new ContinuousTailDetails("")}appendTail(e){return isString(e)&&(e=new ContinuousTailDetails(String(e))),e.appendTo(this)}append(e,t,s){const i=this._appendChar(e[0],t);return null!=s&&(i.tailShift+=this.appendTail(s).tailShift),i}doCommit(){}get state(){return{_value:this._value,_isRawInput:this._isRawInput}}set state(e){Object.assign(this,e)}}const _excluded$3=["chunks"];class ChunksTailDetails{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.chunks=e,this.from=t}toString(){return this.chunks.map(String).join("")}extend(e){if(!String(e))return;isString(e)&&(e=new ContinuousTailDetails(String(e)));const t=this.chunks[this.chunks.length-1],s=t&&(t.stop===e.stop||null==e.stop)&&e.from===t.from+t.toString().length;if(e instanceof ContinuousTailDetails)s?t.extend(e.toString()):this.chunks.push(e);else if(e instanceof ChunksTailDetails){if(null==e.stop){let t;for(;e.chunks.length&&null==e.chunks[0].stop;)t=e.chunks.shift(),t.from+=e.from,this.extend(t)}e.toString()&&(e.stop=e.blockIndex,this.chunks.push(e))}}appendTo(e){if(!(e instanceof IMask.MaskedPattern)){return new ContinuousTailDetails(this.toString()).appendTo(e)}const t=new ChangeDetails;for(let s=0;s<this.chunks.length&&!t.skip;++s){const i=this.chunks[s],n=e._mapPosToBlock(e.value.length),r=i.stop;let a;if(null!=r&&(!n||n.index<=r)){if(i instanceof ChunksTailDetails||e._stops.indexOf(r)>=0){const s=e._appendPlaceholder(r);t.aggregate(s)}a=i instanceof ChunksTailDetails&&e._blocks[r]}if(a){const s=a.appendTail(i);s.skip=!1,t.aggregate(s),e._value+=s.inserted;const n=i.toString().slice(s.rawInserted.length);n&&t.aggregate(e.append(n,{tail:!0}))}else t.aggregate(e.append(i.toString(),{tail:!0}))}return t}get state(){return{chunks:this.chunks.map((e=>e.state)),from:this.from,stop:this.stop,blockIndex:this.blockIndex}}set state(e){const{chunks:t}=e,s=_objectWithoutPropertiesLoose(e,_excluded$3);Object.assign(this,s),this.chunks=t.map((e=>{const t="chunks"in e?new ChunksTailDetails:new ContinuousTailDetails;return t.state=e,t}))}unshift(e){if(!this.chunks.length||null!=e&&this.from>=e)return"";const t=null!=e?e-this.from:e;let s=0;for(;s<this.chunks.length;){const e=this.chunks[s],i=e.unshift(t);if(e.toString()){if(!i)break;++s}else this.chunks.splice(s,1);if(i)return i}return""}shift(){if(!this.chunks.length)return"";let e=this.chunks.length-1;for(;0<=e;){const t=this.chunks[e],s=t.shift();if(t.toString()){if(!s)break;--e}else this.chunks.splice(e,1);if(s)return s}return""}}class PatternCursor{constructor(e,t){this.masked=e,this._log=[];const{offset:s,index:i}=e._mapPosToBlock(t)||(t<0?{index:0,offset:0}:{index:this.masked._blocks.length,offset:0});this.offset=s,this.index=i,this.ok=!1}get block(){return this.masked._blocks[this.index]}get pos(){return this.masked._blockStartPos(this.index)+this.offset}get state(){return{index:this.index,offset:this.offset,ok:this.ok}}set state(e){Object.assign(this,e)}pushState(){this._log.push(this.state)}popState(){const e=this._log.pop();return this.state=e,e}bindBlock(){this.block||(this.index<0&&(this.index=0,this.offset=0),this.index>=this.masked._blocks.length&&(this.index=this.masked._blocks.length-1,this.offset=this.block.value.length))}_pushLeft(e){for(this.pushState(),this.bindBlock();0<=this.index;--this.index,this.offset=(null===(t=this.block)||void 0===t?void 0:t.value.length)||0){var t;if(e())return this.ok=!0}return this.ok=!1}_pushRight(e){for(this.pushState(),this.bindBlock();this.index<this.masked._blocks.length;++this.index,this.offset=0)if(e())return this.ok=!0;return this.ok=!1}pushLeftBeforeFilled(){return this._pushLeft((()=>{if(!this.block.isFixed&&this.block.value)return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.FORCE_LEFT),0!==this.offset||void 0}))}pushLeftBeforeInput(){return this._pushLeft((()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.LEFT),!0}))}pushLeftBeforeRequired(){return this._pushLeft((()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.LEFT),!0}))}pushRightBeforeFilled(){return this._pushRight((()=>{if(!this.block.isFixed&&this.block.value)return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.FORCE_RIGHT),this.offset!==this.block.value.length||void 0}))}pushRightBeforeInput(){return this._pushRight((()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.NONE),!0}))}pushRightBeforeRequired(){return this._pushRight((()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,DIRECTION.NONE),!0}))}}class MaskedRegExp extends Masked{_update(e){e.mask&&(e.validate=t=>t.search(e.mask)>=0),super._update(e)}}IMask.MaskedRegExp=MaskedRegExp;const _excluded$2=["_blocks"];class MaskedPattern extends Masked{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.definitions=Object.assign({},DEFAULT_INPUT_DEFINITIONS,e.definitions),super(Object.assign({},MaskedPattern.DEFAULTS,e))}_update(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.definitions=Object.assign({},this.definitions,e.definitions),super._update(e),this._rebuildMask()}_rebuildMask(){const e=this.definitions;this._blocks=[],this._stops=[],this._maskedBlocks={};let t=this.mask;if(!t||!e)return;let s=!1,i=!1;for(let a=0;a<t.length;++a){var n,r;if(this.blocks){const e=t.slice(a),s=Object.keys(this.blocks).filter((t=>0===e.indexOf(t)));s.sort(((e,t)=>t.length-e.length));const i=s[0];if(i){const e=createMask(Object.assign({parent:this,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar,overwrite:this.overwrite},this.blocks[i]));e&&(this._blocks.push(e),this._maskedBlocks[i]||(this._maskedBlocks[i]=[]),this._maskedBlocks[i].push(this._blocks.length-1)),a+=i.length-1;continue}}let o=t[a],l=o in e;if(o===MaskedPattern.STOP_CHAR){this._stops.push(this._blocks.length);continue}if("{"===o||"}"===o){s=!s;continue}if("["===o||"]"===o){i=!i;continue}if(o===MaskedPattern.ESCAPE_CHAR){if(++a,o=t[a],!o)break;l=!1}const u=null===(n=e[o])||void 0===n||!n.mask||(null===(r=e[o])||void 0===r?void 0:r.mask.prototype)instanceof IMask.Masked?{mask:e[o]}:e[o],c=l?new PatternInputDefinition(Object.assign({parent:this,isOptional:i,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar},u)):new PatternFixedDefinition({char:o,eager:this.eager,isUnmasking:s});this._blocks.push(c)}}get state(){return Object.assign({},super.state,{_blocks:this._blocks.map((e=>e.state))})}set state(e){const{_blocks:t}=e,s=_objectWithoutPropertiesLoose(e,_excluded$2);this._blocks.forEach(((e,s)=>e.state=t[s])),super.state=s}reset(){super.reset(),this._blocks.forEach((e=>e.reset()))}get isComplete(){return this._blocks.every((e=>e.isComplete))}get isFilled(){return this._blocks.every((e=>e.isFilled))}get isFixed(){return this._blocks.every((e=>e.isFixed))}get isOptional(){return this._blocks.every((e=>e.isOptional))}doCommit(){this._blocks.forEach((e=>e.doCommit())),super.doCommit()}get unmaskedValue(){return this._blocks.reduce(((e,t)=>e+t.unmaskedValue),"")}set unmaskedValue(e){super.unmaskedValue=e}get value(){return this._blocks.reduce(((e,t)=>e+t.value),"")}set value(e){super.value=e}get displayValue(){return this._blocks.reduce(((e,t)=>e+t.displayValue),"")}appendTail(e){return super.appendTail(e).aggregate(this._appendPlaceholder())}_appendEager(){var e;const t=new ChangeDetails;let s=null===(e=this._mapPosToBlock(this.value.length))||void 0===e?void 0:e.index;if(null==s)return t;this._blocks[s].isFilled&&++s;for(let e=s;e<this._blocks.length;++e){const s=this._blocks[e]._appendEager();if(!s.inserted)break;t.aggregate(s)}return t}_appendCharRaw(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this._mapPosToBlock(this.value.length),i=new ChangeDetails;if(!s)return i;for(let a=s.index;;++a){var n,r;const s=this._blocks[a];if(!s)break;const o=s._appendChar(e,Object.assign({},t,{_beforeTailState:null===(n=t._beforeTailState)||void 0===n||null===(r=n._blocks)||void 0===r?void 0:r[a]})),l=o.skip;if(i.aggregate(o),l||o.rawInserted)break}return i}extractTail(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;const s=new ChunksTailDetails;return e===t||this._forEachBlocksInRange(e,t,((e,t,i,n)=>{const r=e.extractTail(i,n);r.stop=this._findStopBefore(t),r.from=this._blockStartPos(t),r instanceof ChunksTailDetails&&(r.blockIndex=t),s.extend(r)})),s}extractInput(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e===t)return"";let i="";return this._forEachBlocksInRange(e,t,((e,t,n,r)=>{i+=e.extractInput(n,r,s)})),i}_findStopBefore(e){let t;for(let s=0;s<this._stops.length;++s){const i=this._stops[s];if(!(i<=e))break;t=i}return t}_appendPlaceholder(e){const t=new ChangeDetails;if(this.lazy&&null==e)return t;const s=this._mapPosToBlock(this.value.length);if(!s)return t;const i=s.index,n=null!=e?e:this._blocks.length;return this._blocks.slice(i,n).forEach((s=>{if(!s.lazy||null!=e){const e=null!=s._blocks?[s._blocks.length]:[],i=s._appendPlaceholder(...e);this._value+=i.inserted,t.aggregate(i)}})),t}_mapPosToBlock(e){let t="";for(let s=0;s<this._blocks.length;++s){const i=this._blocks[s],n=t.length;if(t+=i.value,e<=t.length)return{index:s,offset:e-n}}}_blockStartPos(e){return this._blocks.slice(0,e).reduce(((e,t)=>e+t.value.length),0)}_forEachBlocksInRange(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0;const i=this._mapPosToBlock(e);if(i){const e=this._mapPosToBlock(t),n=e&&i.index===e.index,r=i.offset,a=e&&n?e.offset:this._blocks[i.index].value.length;if(s(this._blocks[i.index],i.index,r,a),e&&!n){for(let t=i.index+1;t<e.index;++t)s(this._blocks[t],t,0,this._blocks[t].value.length);s(this._blocks[e.index],e.index,0,e.offset)}}}remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;const s=super.remove(e,t);return this._forEachBlocksInRange(e,t,((e,t,i,n)=>{s.aggregate(e.remove(i,n))})),s}nearestInputPos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:DIRECTION.NONE;if(!this._blocks.length)return 0;const s=new PatternCursor(this,e);if(t===DIRECTION.NONE)return s.pushRightBeforeInput()?s.pos:(s.popState(),s.pushLeftBeforeInput()?s.pos:this.value.length);if(t===DIRECTION.LEFT||t===DIRECTION.FORCE_LEFT){if(t===DIRECTION.LEFT){if(s.pushRightBeforeFilled(),s.ok&&s.pos===e)return e;s.popState()}if(s.pushLeftBeforeInput(),s.pushLeftBeforeRequired(),s.pushLeftBeforeFilled(),t===DIRECTION.LEFT){if(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.ok&&s.pos<=e)return s.pos;if(s.popState(),s.ok&&s.pos<=e)return s.pos;s.popState()}return s.ok?s.pos:t===DIRECTION.FORCE_LEFT?0:(s.popState(),s.ok?s.pos:(s.popState(),s.ok?s.pos:0))}return t===DIRECTION.RIGHT||t===DIRECTION.FORCE_RIGHT?(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.pushRightBeforeFilled()?s.pos:t===DIRECTION.FORCE_RIGHT?this.value.length:(s.popState(),s.ok?s.pos:(s.popState(),s.ok?s.pos:this.nearestInputPos(e,DIRECTION.LEFT)))):e}totalInputPositions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,s=0;return this._forEachBlocksInRange(e,t,((e,t,i,n)=>{s+=e.totalInputPositions(i,n)})),s}maskedBlock(e){return this.maskedBlocks(e)[0]}maskedBlocks(e){const t=this._maskedBlocks[e];return t?t.map((e=>this._blocks[e])):[]}}MaskedPattern.DEFAULTS={lazy:!0,placeholderChar:"_"},MaskedPattern.STOP_CHAR="`",MaskedPattern.ESCAPE_CHAR="\\",MaskedPattern.InputDefinition=PatternInputDefinition,MaskedPattern.FixedDefinition=PatternFixedDefinition,IMask.MaskedPattern=MaskedPattern;class MaskedRange extends MaskedPattern{get _matchFrom(){return this.maxLength-String(this.from).length}_update(e){e=Object.assign({to:this.to||0,from:this.from||0,maxLength:this.maxLength||0},e);let t=String(e.to).length;null!=e.maxLength&&(t=Math.max(t,e.maxLength)),e.maxLength=t;const s=String(e.from).padStart(t,"0"),i=String(e.to).padStart(t,"0");let n=0;for(;n<i.length&&i[n]===s[n];)++n;e.mask=i.slice(0,n).replace(/0/g,"\\0")+"0".repeat(t-n),super._update(e)}get isComplete(){return super.isComplete&&Boolean(this.value)}boundaries(e){let t="",s="";const[,i,n]=e.match(/^(\D*)(\d*)(\D*)/)||[];return n&&(t="0".repeat(i.length)+n,s="9".repeat(i.length)+n),t=t.padEnd(this.maxLength,"0"),s=s.padEnd(this.maxLength,"9"),[t,s]}doPrepare(e){let t,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if([e,t]=normalizePrepare(super.doPrepare(e.replace(/\D/g,""),s)),!this.autofix||!e)return e;const i=String(this.from).padStart(this.maxLength,"0"),n=String(this.to).padStart(this.maxLength,"0");let r=this.value+e;if(r.length>this.maxLength)return"";const[a,o]=this.boundaries(r);return Number(o)<this.from?i[r.length-1]:Number(a)>this.to?"pad"===this.autofix&&r.length<this.maxLength?["",t.aggregate(this.append(i[r.length-1]+e,s))]:n[r.length-1]:e}doValidate(){const e=this.value;if(-1===e.search(/[^0]/)&&e.length<=this._matchFrom)return!0;const[t,s]=this.boundaries(e);return this.from<=Number(s)&&Number(t)<=this.to&&super.doValidate(...arguments)}}IMask.MaskedRange=MaskedRange;class MaskedDate extends MaskedPattern{constructor(e){super(Object.assign({},MaskedDate.DEFAULTS,e))}_update(e){e.mask===Date&&delete e.mask,e.pattern&&(e.mask=e.pattern);const t=e.blocks;e.blocks=Object.assign({},MaskedDate.GET_DEFAULT_BLOCKS()),e.min&&(e.blocks.Y.from=e.min.getFullYear()),e.max&&(e.blocks.Y.to=e.max.getFullYear()),e.min&&e.max&&e.blocks.Y.from===e.blocks.Y.to&&(e.blocks.m.from=e.min.getMonth()+1,e.blocks.m.to=e.max.getMonth()+1,e.blocks.m.from===e.blocks.m.to&&(e.blocks.d.from=e.min.getDate(),e.blocks.d.to=e.max.getDate())),Object.assign(e.blocks,this.blocks,t),Object.keys(e.blocks).forEach((t=>{const s=e.blocks[t];!("autofix"in s)&&"autofix"in e&&(s.autofix=e.autofix)})),super._update(e)}doValidate(){const e=this.date;return super.doValidate(...arguments)&&(!this.isComplete||this.isDateExist(this.value)&&null!=e&&(null==this.min||this.min<=e)&&(null==this.max||e<=this.max))}isDateExist(e){return this.format(this.parse(e,this),this).indexOf(e)>=0}get date(){return this.typedValue}set date(e){this.typedValue=e}get typedValue(){return this.isComplete?super.typedValue:null}set typedValue(e){super.typedValue=e}maskEquals(e){return e===Date||super.maskEquals(e)}}MaskedDate.DEFAULTS={pattern:"d{.}`m{.}`Y",format:e=>{if(!e)return"";return[String(e.getDate()).padStart(2,"0"),String(e.getMonth()+1).padStart(2,"0"),e.getFullYear()].join(".")},parse:e=>{const[t,s,i]=e.split(".");return new Date(i,s-1,t)}},MaskedDate.GET_DEFAULT_BLOCKS=()=>({d:{mask:MaskedRange,from:1,to:31,maxLength:2},m:{mask:MaskedRange,from:1,to:12,maxLength:2},Y:{mask:MaskedRange,from:1900,to:9999}}),IMask.MaskedDate=MaskedDate;class MaskElement{get selectionStart(){let e;try{e=this._unsafeSelectionStart}catch(e){}return null!=e?e:this.value.length}get selectionEnd(){let e;try{e=this._unsafeSelectionEnd}catch(e){}return null!=e?e:this.value.length}select(e,t){if(null!=e&&null!=t&&(e!==this.selectionStart||t!==this.selectionEnd))try{this._unsafeSelect(e,t)}catch(e){}}_unsafeSelect(e,t){}get isActive(){return!1}bindEvents(e){}unbindEvents(){}}IMask.MaskElement=MaskElement;class HTMLMaskElement extends MaskElement{constructor(e){super(),this.input=e,this._handlers={}}get rootElement(){var e,t,s;return null!==(e=null===(t=(s=this.input).getRootNode)||void 0===t?void 0:t.call(s))&&void 0!==e?e:document}get isActive(){return this.input===this.rootElement.activeElement}get _unsafeSelectionStart(){return this.input.selectionStart}get _unsafeSelectionEnd(){return this.input.selectionEnd}_unsafeSelect(e,t){this.input.setSelectionRange(e,t)}get value(){return this.input.value}set value(e){this.input.value=e}bindEvents(e){Object.keys(e).forEach((t=>this._toggleEventHandler(HTMLMaskElement.EVENTS_MAP[t],e[t])))}unbindEvents(){Object.keys(this._handlers).forEach((e=>this._toggleEventHandler(e)))}_toggleEventHandler(e,t){this._handlers[e]&&(this.input.removeEventListener(e,this._handlers[e]),delete this._handlers[e]),t&&(this.input.addEventListener(e,t),this._handlers[e]=t)}}HTMLMaskElement.EVENTS_MAP={selectionChange:"keydown",input:"input",drop:"drop",click:"click",focus:"focus",commit:"blur"},IMask.HTMLMaskElement=HTMLMaskElement;class HTMLContenteditableMaskElement extends HTMLMaskElement{get _unsafeSelectionStart(){const e=this.rootElement,t=e.getSelection&&e.getSelection(),s=t&&t.anchorOffset,i=t&&t.focusOffset;return null==i||null==s||s<i?s:i}get _unsafeSelectionEnd(){const e=this.rootElement,t=e.getSelection&&e.getSelection(),s=t&&t.anchorOffset,i=t&&t.focusOffset;return null==i||null==s||s>i?s:i}_unsafeSelect(e,t){if(!this.rootElement.createRange)return;const s=this.rootElement.createRange();s.setStart(this.input.firstChild||this.input,e),s.setEnd(this.input.lastChild||this.input,t);const i=this.rootElement,n=i.getSelection&&i.getSelection();n&&(n.removeAllRanges(),n.addRange(s))}get value(){return this.input.textContent}set value(e){this.input.textContent=e}}IMask.HTMLContenteditableMaskElement=HTMLContenteditableMaskElement;const _excluded$1=["mask"];class InputMask{constructor(e,t){this.el=e instanceof MaskElement?e:e.isContentEditable&&"INPUT"!==e.tagName&&"TEXTAREA"!==e.tagName?new HTMLContenteditableMaskElement(e):new HTMLMaskElement(e),this.masked=createMask(t),this._listeners={},this._value="",this._unmaskedValue="",this._saveSelection=this._saveSelection.bind(this),this._onInput=this._onInput.bind(this),this._onChange=this._onChange.bind(this),this._onDrop=this._onDrop.bind(this),this._onFocus=this._onFocus.bind(this),this._onClick=this._onClick.bind(this),this.alignCursor=this.alignCursor.bind(this),this.alignCursorFriendly=this.alignCursorFriendly.bind(this),this._bindEvents(),this.updateValue(),this._onChange()}get mask(){return this.masked.mask}maskEquals(e){var t;return null==e||(null===(t=this.masked)||void 0===t?void 0:t.maskEquals(e))}set mask(e){if(this.maskEquals(e))return;if(!(e instanceof IMask.Masked)&&this.masked.constructor===maskedClass(e))return void this.masked.updateOptions({mask:e});const t=createMask({mask:e});t.unmaskedValue=this.masked.unmaskedValue,this.masked=t}get value(){return this._value}set value(e){this.value!==e&&(this.masked.value=e,this.updateControl(),this.alignCursor())}get unmaskedValue(){return this._unmaskedValue}set unmaskedValue(e){this.unmaskedValue!==e&&(this.masked.unmaskedValue=e,this.updateControl(),this.alignCursor())}get typedValue(){return this.masked.typedValue}set typedValue(e){this.masked.typedValueEquals(e)||(this.masked.typedValue=e,this.updateControl(),this.alignCursor())}get displayValue(){return this.masked.displayValue}_bindEvents(){this.el.bindEvents({selectionChange:this._saveSelection,input:this._onInput,drop:this._onDrop,click:this._onClick,focus:this._onFocus,commit:this._onChange})}_unbindEvents(){this.el&&this.el.unbindEvents()}_fireEvent(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];const n=this._listeners[e];n&&n.forEach((e=>e(...s)))}get selectionStart(){return this._cursorChanging?this._changingCursorPos:this.el.selectionStart}get cursorPos(){return this._cursorChanging?this._changingCursorPos:this.el.selectionEnd}set cursorPos(e){this.el&&this.el.isActive&&(this.el.select(e,e),this._saveSelection())}_saveSelection(){this.displayValue!==this.el.value&&console.warn("Element value was changed outside of mask. Syncronize mask using `mask.updateValue()` to work properly."),this._selection={start:this.selectionStart,end:this.cursorPos}}updateValue(){this.masked.value=this.el.value,this._value=this.masked.value}updateControl(){const e=this.masked.unmaskedValue,t=this.masked.value,s=this.displayValue,i=this.unmaskedValue!==e||this.value!==t;this._unmaskedValue=e,this._value=t,this.el.value!==s&&(this.el.value=s),i&&this._fireChangeEvents()}updateOptions(e){const{mask:t}=e,s=_objectWithoutPropertiesLoose(e,_excluded$1),i=!this.maskEquals(t),n=!objectIncludes(this.masked,s);i&&(this.mask=t),n&&this.masked.updateOptions(s),(i||n)&&this.updateControl()}updateCursor(e){null!=e&&(this.cursorPos=e,this._delayUpdateCursor(e))}_delayUpdateCursor(e){this._abortUpdateCursor(),this._changingCursorPos=e,this._cursorChanging=setTimeout((()=>{this.el&&(this.cursorPos=this._changingCursorPos,this._abortUpdateCursor())}),10)}_fireChangeEvents(){this._fireEvent("accept",this._inputEvent),this.masked.isComplete&&this._fireEvent("complete",this._inputEvent)}_abortUpdateCursor(){this._cursorChanging&&(clearTimeout(this._cursorChanging),delete this._cursorChanging)}alignCursor(){this.cursorPos=this.masked.nearestInputPos(this.masked.nearestInputPos(this.cursorPos,DIRECTION.LEFT))}alignCursorFriendly(){this.selectionStart===this.cursorPos&&this.alignCursor()}on(e,t){return this._listeners[e]||(this._listeners[e]=[]),this._listeners[e].push(t),this}off(e,t){if(!this._listeners[e])return this;if(!t)return delete this._listeners[e],this;const s=this._listeners[e].indexOf(t);return s>=0&&this._listeners[e].splice(s,1),this}_onInput(e){if(this._inputEvent=e,this._abortUpdateCursor(),!this._selection)return this.updateValue();const t=new ActionDetails(this.el.value,this.cursorPos,this.displayValue,this._selection),s=this.masked.rawInputValue,i=this.masked.splice(t.startChangePos,t.removed.length,t.inserted,t.removeDirection,{input:!0,raw:!0}).offset,n=s===this.masked.rawInputValue?t.removeDirection:DIRECTION.NONE;let r=this.masked.nearestInputPos(t.startChangePos+i,n);n!==DIRECTION.NONE&&(r=this.masked.nearestInputPos(r,DIRECTION.NONE)),this.updateControl(),this.updateCursor(r),delete this._inputEvent}_onChange(){this.displayValue!==this.el.value&&this.updateValue(),this.masked.doCommit(),this.updateControl(),this._saveSelection()}_onDrop(e){e.preventDefault(),e.stopPropagation()}_onFocus(e){this.alignCursorFriendly()}_onClick(e){this.alignCursorFriendly()}destroy(){this._unbindEvents(),this._listeners.length=0,delete this.el}}IMask.InputMask=InputMask;class MaskedEnum extends MaskedPattern{_update(e){e.enum&&(e.mask="*".repeat(e.enum[0].length)),super._update(e)}doValidate(){return this.enum.some((e=>e.indexOf(this.unmaskedValue)>=0))&&super.doValidate(...arguments)}}IMask.MaskedEnum=MaskedEnum;class MaskedNumber extends Masked{constructor(e){super(Object.assign({},MaskedNumber.DEFAULTS,e))}_update(e){super._update(e),this._updateRegExps()}_updateRegExps(){let e="^"+(this.allowNegative?"[+|\\-]?":""),t=(this.scale?"(".concat(escapeRegExp(this.radix),"\\d{0,").concat(this.scale,"})?"):"")+"$";this._numberRegExp=new RegExp(e+"\\d*"+t),this._mapToRadixRegExp=new RegExp("[".concat(this.mapToRadix.map(escapeRegExp).join(""),"]"),"g"),this._thousandsSeparatorRegExp=new RegExp(escapeRegExp(this.thousandsSeparator),"g")}_removeThousandsSeparators(e){return e.replace(this._thousandsSeparatorRegExp,"")}_insertThousandsSeparators(e){const t=e.split(this.radix);return t[0]=t[0].replace(/\B(?=(\d{3})+(?!\d))/g,this.thousandsSeparator),t.join(this.radix)}doPrepare(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e=this._removeThousandsSeparators(this.scale&&this.mapToRadix.length&&(t.input&&t.raw||!t.input&&!t.raw)?e.replace(this._mapToRadixRegExp,this.radix):e);const[s,i]=normalizePrepare(super.doPrepare(e,t));return e&&!s&&(i.skip=!0),[s,i]}_separatorsCount(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=0;for(let i=0;i<e;++i)this._value.indexOf(this.thousandsSeparator,i)===i&&(++s,t&&(e+=this.thousandsSeparator.length));return s}_separatorsCountFromSlice(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._value;return this._separatorsCount(this._removeThousandsSeparators(e).length,!0)}extractInput(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length,s=arguments.length>2?arguments[2]:void 0;return[e,t]=this._adjustRangeWithSeparators(e,t),this._removeThousandsSeparators(super.extractInput(e,t,s))}_appendCharRaw(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.thousandsSeparator)return super._appendCharRaw(e,t);const s=t.tail&&t._beforeTailState?t._beforeTailState._value:this._value,i=this._separatorsCountFromSlice(s);this._value=this._removeThousandsSeparators(this.value);const n=super._appendCharRaw(e,t);this._value=this._insertThousandsSeparators(this._value);const r=t.tail&&t._beforeTailState?t._beforeTailState._value:this._value,a=this._separatorsCountFromSlice(r);return n.tailShift+=(a-i)*this.thousandsSeparator.length,n.skip=!n.rawInserted&&e===this.thousandsSeparator,n}_findSeparatorAround(e){if(this.thousandsSeparator){const t=e-this.thousandsSeparator.length+1,s=this.value.indexOf(this.thousandsSeparator,t);if(s<=e)return s}return-1}_adjustRangeWithSeparators(e,t){const s=this._findSeparatorAround(e);s>=0&&(e=s);const i=this._findSeparatorAround(t);return i>=0&&(t=i+this.thousandsSeparator.length),[e,t]}remove(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value.length;[e,t]=this._adjustRangeWithSeparators(e,t);const s=this.value.slice(0,e),i=this.value.slice(t),n=this._separatorsCount(s.length);this._value=this._insertThousandsSeparators(this._removeThousandsSeparators(s+i));const r=this._separatorsCountFromSlice(s);return new ChangeDetails({tailShift:(r-n)*this.thousandsSeparator.length})}nearestInputPos(e,t){if(!this.thousandsSeparator)return e;switch(t){case DIRECTION.NONE:case DIRECTION.LEFT:case DIRECTION.FORCE_LEFT:{const s=this._findSeparatorAround(e-1);if(s>=0){const i=s+this.thousandsSeparator.length;if(e<i||this.value.length<=i||t===DIRECTION.FORCE_LEFT)return s}break}case DIRECTION.RIGHT:case DIRECTION.FORCE_RIGHT:{const t=this._findSeparatorAround(e);if(t>=0)return t+this.thousandsSeparator.length}}return e}doValidate(e){let t=Boolean(this._removeThousandsSeparators(this.value).match(this._numberRegExp));if(t){const e=this.number;t=t&&!isNaN(e)&&(null==this.min||this.min>=0||this.min<=this.number)&&(null==this.max||this.max<=0||this.number<=this.max)}return t&&super.doValidate(e)}doCommit(){if(this.value){const e=this.number;let t=e;null!=this.min&&(t=Math.max(t,this.min)),null!=this.max&&(t=Math.min(t,this.max)),t!==e&&(this.unmaskedValue=this.doFormat(t));let s=this.value;this.normalizeZeros&&(s=this._normalizeZeros(s)),this.padFractionalZeros&&this.scale>0&&(s=this._padFractionalZeros(s)),this._value=s}super.doCommit()}_normalizeZeros(e){const t=this._removeThousandsSeparators(e).split(this.radix);return t[0]=t[0].replace(/^(\D*)(0*)(\d*)/,((e,t,s,i)=>t+i)),e.length&&!/\d$/.test(t[0])&&(t[0]=t[0]+"0"),t.length>1&&(t[1]=t[1].replace(/0*$/,""),t[1].length||(t.length=1)),this._insertThousandsSeparators(t.join(this.radix))}_padFractionalZeros(e){if(!e)return e;const t=e.split(this.radix);return t.length<2&&t.push(""),t[1]=t[1].padEnd(this.scale,"0"),t.join(this.radix)}doSkipInvalid(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0;const i=0===this.scale&&e!==this.thousandsSeparator&&(e===this.radix||e===MaskedNumber.UNMASKED_RADIX||this.mapToRadix.includes(e));return super.doSkipInvalid(e,t,s)&&!i}get unmaskedValue(){return this._removeThousandsSeparators(this._normalizeZeros(this.value)).replace(this.radix,MaskedNumber.UNMASKED_RADIX)}set unmaskedValue(e){super.unmaskedValue=e}get typedValue(){return this.doParse(this.unmaskedValue)}set typedValue(e){this.rawInputValue=this.doFormat(e).replace(MaskedNumber.UNMASKED_RADIX,this.radix)}get number(){return this.typedValue}set number(e){this.typedValue=e}get allowNegative(){return this.signed||null!=this.min&&this.min<0||null!=this.max&&this.max<0}typedValueEquals(e){return(super.typedValueEquals(e)||MaskedNumber.EMPTY_VALUES.includes(e)&&MaskedNumber.EMPTY_VALUES.includes(this.typedValue))&&!(0===e&&""===this.value)}}MaskedNumber.UNMASKED_RADIX=".",MaskedNumber.DEFAULTS={radix:",",thousandsSeparator:"",mapToRadix:[MaskedNumber.UNMASKED_RADIX],scale:2,signed:!1,normalizeZeros:!0,padFractionalZeros:!1,parse:Number,format:e=>e.toLocaleString("en-US",{useGrouping:!1,maximumFractionDigits:20})},MaskedNumber.EMPTY_VALUES=[...Masked.EMPTY_VALUES,0],IMask.MaskedNumber=MaskedNumber;class MaskedFunction extends Masked{_update(e){e.mask&&(e.validate=e.mask),super._update(e)}}IMask.MaskedFunction=MaskedFunction;const _excluded=["compiledMasks","currentMaskRef","currentMask"],_excluded2=["mask"];class MaskedDynamic extends Masked{constructor(e){super(Object.assign({},MaskedDynamic.DEFAULTS,e)),this.currentMask=null}_update(e){super._update(e),"mask"in e&&(this.compiledMasks=Array.isArray(e.mask)?e.mask.map((e=>createMask(e))):[])}_appendCharRaw(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this._applyDispatch(e,t);return this.currentMask&&s.aggregate(this.currentMask._appendChar(e,this.currentMaskFlags(t))),s}_applyDispatch(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";const i=t.tail&&null!=t._beforeTailState?t._beforeTailState._value:this.value,n=this.rawInputValue,r=t.tail&&null!=t._beforeTailState?t._beforeTailState._rawInputValue:n,a=n.slice(r.length),o=this.currentMask,l=new ChangeDetails,u=null==o?void 0:o.state;if(this.currentMask=this.doDispatch(e,Object.assign({},t),s),this.currentMask)if(this.currentMask!==o){if(this.currentMask.reset(),r){const e=this.currentMask.append(r,{raw:!0});l.tailShift=e.inserted.length-i.length}a&&(l.tailShift+=this.currentMask.append(a,{raw:!0,tail:!0}).tailShift)}else this.currentMask.state=u;return l}_appendPlaceholder(){const e=this._applyDispatch(...arguments);return this.currentMask&&e.aggregate(this.currentMask._appendPlaceholder()),e}_appendEager(){const e=this._applyDispatch(...arguments);return this.currentMask&&e.aggregate(this.currentMask._appendEager()),e}appendTail(e){const t=new ChangeDetails;return e&&t.aggregate(this._applyDispatch("",{},e)),t.aggregate(this.currentMask?this.currentMask.appendTail(e):super.appendTail(e))}currentMaskFlags(e){var t,s;return Object.assign({},e,{_beforeTailState:(null===(t=e._beforeTailState)||void 0===t?void 0:t.currentMaskRef)===this.currentMask&&(null===(s=e._beforeTailState)||void 0===s?void 0:s.currentMask)||e._beforeTailState})}doDispatch(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return this.dispatch(e,this,t,s)}doValidate(e){return super.doValidate(e)&&(!this.currentMask||this.currentMask.doValidate(this.currentMaskFlags(e)))}doPrepare(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},[s,i]=normalizePrepare(super.doPrepare(e,t));if(this.currentMask){let e;[s,e]=normalizePrepare(super.doPrepare(s,this.currentMaskFlags(t))),i=i.aggregate(e)}return[s,i]}reset(){var e;null===(e=this.currentMask)||void 0===e||e.reset(),this.compiledMasks.forEach((e=>e.reset()))}get value(){return this.currentMask?this.currentMask.value:""}set value(e){super.value=e}get unmaskedValue(){return this.currentMask?this.currentMask.unmaskedValue:""}set unmaskedValue(e){super.unmaskedValue=e}get typedValue(){return this.currentMask?this.currentMask.typedValue:""}set typedValue(e){let t=String(e);this.currentMask&&(this.currentMask.typedValue=e,t=this.currentMask.unmaskedValue),this.unmaskedValue=t}get displayValue(){return this.currentMask?this.currentMask.displayValue:""}get isComplete(){var e;return Boolean(null===(e=this.currentMask)||void 0===e?void 0:e.isComplete)}get isFilled(){var e;return Boolean(null===(e=this.currentMask)||void 0===e?void 0:e.isFilled)}remove(){const e=new ChangeDetails;return this.currentMask&&e.aggregate(this.currentMask.remove(...arguments)).aggregate(this._applyDispatch()),e}get state(){var e;return Object.assign({},super.state,{_rawInputValue:this.rawInputValue,compiledMasks:this.compiledMasks.map((e=>e.state)),currentMaskRef:this.currentMask,currentMask:null===(e=this.currentMask)||void 0===e?void 0:e.state})}set state(e){const{compiledMasks:t,currentMaskRef:s,currentMask:i}=e,n=_objectWithoutPropertiesLoose(e,_excluded);this.compiledMasks.forEach(((e,s)=>e.state=t[s])),null!=s&&(this.currentMask=s,this.currentMask.state=i),super.state=n}extractInput(){return this.currentMask?this.currentMask.extractInput(...arguments):""}extractTail(){return this.currentMask?this.currentMask.extractTail(...arguments):super.extractTail(...arguments)}doCommit(){this.currentMask&&this.currentMask.doCommit(),super.doCommit()}nearestInputPos(){return this.currentMask?this.currentMask.nearestInputPos(...arguments):super.nearestInputPos(...arguments)}get overwrite(){return this.currentMask?this.currentMask.overwrite:super.overwrite}set overwrite(e){console.warn('"overwrite" option is not available in dynamic mask, use this option in siblings')}get eager(){return this.currentMask?this.currentMask.eager:super.eager}set eager(e){console.warn('"eager" option is not available in dynamic mask, use this option in siblings')}get skipInvalid(){return this.currentMask?this.currentMask.skipInvalid:super.skipInvalid}set skipInvalid(e){(this.isInitialized||e!==Masked.DEFAULTS.skipInvalid)&&console.warn('"skipInvalid" option is not available in dynamic mask, use this option in siblings')}maskEquals(e){return Array.isArray(e)&&this.compiledMasks.every(((t,s)=>{if(!e[s])return;const i=e[s],{mask:n}=i;return objectIncludes(t,_objectWithoutPropertiesLoose(i,_excluded2))&&t.maskEquals(n)}))}typedValueEquals(e){var t;return Boolean(null===(t=this.currentMask)||void 0===t?void 0:t.typedValueEquals(e))}}MaskedDynamic.DEFAULTS={dispatch:(e,t,s,i)=>{if(!t.compiledMasks.length)return;const n=t.rawInputValue,r=t.compiledMasks.map(((r,a)=>{const o=t.currentMask===r,l=o?r.value.length:r.nearestInputPos(r.value.length,DIRECTION.FORCE_LEFT);return r.rawInputValue!==n?(r.reset(),r.append(n,{raw:!0})):o||r.remove(l),r.append(e,t.currentMaskFlags(s)),r.appendTail(i),{index:a,weight:r.rawInputValue.length,totalInputPositions:r.totalInputPositions(0,Math.max(l,r.nearestInputPos(r.value.length,DIRECTION.FORCE_LEFT)))}}));return r.sort(((e,t)=>t.weight-e.weight||t.totalInputPositions-e.totalInputPositions)),t.compiledMasks[r[0].index]}},IMask.MaskedDynamic=MaskedDynamic;const PIPE_TYPE={MASKED:"value",UNMASKED:"unmaskedValue",TYPED:"typedValue"};function createPipe(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:PIPE_TYPE.MASKED,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:PIPE_TYPE.MASKED;const i=createMask(e);return e=>i.runIsolated((i=>(i[t]=e,i[s])))}function pipe(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];return createPipe(...s)(e)}IMask.PIPE_TYPE=PIPE_TYPE,IMask.createPipe=createPipe,IMask.pipe=pipe;try{globalThis.IMask=IMask}catch(e){}var maskElementList=[].slice.call(document.querySelectorAll("[data-mask]"));maskElementList.map((function(e){return new IMask(e,{mask:e.dataset.mask,lazy:"true"===e.dataset["mask-visible"]})}));var top="top",bottom="bottom",right="right",left="left",auto="auto",basePlacements=[top,bottom,right,left],start="start",end="end",clippingParents="clippingParents",viewport="viewport",popper="popper",reference="reference",variationPlacements=basePlacements.reduce((function(e,t){return e.concat([t+"-"+start,t+"-"+end])}),[]),placements=[].concat(basePlacements,[auto]).reduce((function(e,t){return e.concat([t,t+"-"+start,t+"-"+end])}),[]),beforeRead="beforeRead",read="read",afterRead="afterRead",beforeMain="beforeMain",main="main",afterMain="afterMain",beforeWrite="beforeWrite",write="write",afterWrite="afterWrite",modifierPhases=[beforeRead,read,afterRead,beforeMain,main,afterMain,beforeWrite,write,afterWrite];function getNodeName(e){return e?(e.nodeName||"").toLowerCase():null}function getWindow(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function isElement$1(e){return e instanceof getWindow(e).Element||e instanceof Element}function isHTMLElement(e){return e instanceof getWindow(e).HTMLElement||e instanceof HTMLElement}function isShadowRoot(e){return"undefined"!=typeof ShadowRoot&&(e instanceof getWindow(e).ShadowRoot||e instanceof ShadowRoot)}function applyStyles(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var s=t.styles[e]||{},i=t.attributes[e]||{},n=t.elements[e];isHTMLElement(n)&&getNodeName(n)&&(Object.assign(n.style,s),Object.keys(i).forEach((function(e){var t=i[e];!1===t?n.removeAttribute(e):n.setAttribute(e,!0===t?"":t)})))}))}function effect$2(e){var t=e.state,s={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,s.popper),t.styles=s,t.elements.arrow&&Object.assign(t.elements.arrow.style,s.arrow),function(){Object.keys(t.elements).forEach((function(e){var i=t.elements[e],n=t.attributes[e]||{},r=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:s[e]).reduce((function(e,t){return e[t]="",e}),{});isHTMLElement(i)&&getNodeName(i)&&(Object.assign(i.style,r),Object.keys(n).forEach((function(e){i.removeAttribute(e)})))}))}}var applyStyles$1={name:"applyStyles",enabled:!0,phase:"write",fn:applyStyles,effect:effect$2,requires:["computeStyles"]};function getBasePlacement(e){return e.split("-")[0]}var max=Math.max,min=Math.min,round=Math.round;function getUAString(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function isLayoutViewport(){return!/^((?!chrome|android).)*safari/i.test(getUAString())}function getBoundingClientRect(e,t,s){void 0===t&&(t=!1),void 0===s&&(s=!1);var i=e.getBoundingClientRect(),n=1,r=1;t&&isHTMLElement(e)&&(n=e.offsetWidth>0&&round(i.width)/e.offsetWidth||1,r=e.offsetHeight>0&&round(i.height)/e.offsetHeight||1);var a=(isElement$1(e)?getWindow(e):window).visualViewport,o=!isLayoutViewport()&&s,l=(i.left+(o&&a?a.offsetLeft:0))/n,u=(i.top+(o&&a?a.offsetTop:0))/r,c=i.width/n,h=i.height/r;return{width:c,height:h,top:u,right:l+c,bottom:u+h,left:l,x:l,y:u}}function getLayoutRect(e){var t=getBoundingClientRect(e),s=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-s)<=1&&(s=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:s,height:i}}function contains(e,t){var s=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(s&&isShadowRoot(s)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function getComputedStyle$1(e){return getWindow(e).getComputedStyle(e)}function isTableElement(e){return["table","td","th"].indexOf(getNodeName(e))>=0}function getDocumentElement(e){return((isElement$1(e)?e.ownerDocument:e.document)||window.document).documentElement}function getParentNode(e){return"html"===getNodeName(e)?e:e.assignedSlot||e.parentNode||(isShadowRoot(e)?e.host:null)||getDocumentElement(e)}function getTrueOffsetParent(e){return isHTMLElement(e)&&"fixed"!==getComputedStyle$1(e).position?e.offsetParent:null}function getContainingBlock(e){var t=/firefox/i.test(getUAString());if(/Trident/i.test(getUAString())&&isHTMLElement(e)&&"fixed"===getComputedStyle$1(e).position)return null;var s=getParentNode(e);for(isShadowRoot(s)&&(s=s.host);isHTMLElement(s)&&["html","body"].indexOf(getNodeName(s))<0;){var i=getComputedStyle$1(s);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return s;s=s.parentNode}return null}function getOffsetParent(e){for(var t=getWindow(e),s=getTrueOffsetParent(e);s&&isTableElement(s)&&"static"===getComputedStyle$1(s).position;)s=getTrueOffsetParent(s);return s&&("html"===getNodeName(s)||"body"===getNodeName(s)&&"static"===getComputedStyle$1(s).position)?t:s||getContainingBlock(e)||t}function getMainAxisFromPlacement(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function within(e,t,s){return max(e,min(t,s))}function withinMaxClamp(e,t,s){var i=within(e,t,s);return i>s?s:i}function getFreshSideObject(){return{top:0,right:0,bottom:0,left:0}}function mergePaddingObject(e){return Object.assign({},getFreshSideObject(),e)}function expandToHashMap(e,t){return t.reduce((function(t,s){return t[s]=e,t}),{})}var toPaddingObject=function(e,t){return mergePaddingObject("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:expandToHashMap(e,basePlacements))};function arrow(e){var t,s=e.state,i=e.name,n=e.options,r=s.elements.arrow,a=s.modifiersData.popperOffsets,o=getBasePlacement(s.placement),l=getMainAxisFromPlacement(o),u=[left,right].indexOf(o)>=0?"height":"width";if(r&&a){var c=toPaddingObject(n.padding,s),h=getLayoutRect(r),d="y"===l?top:left,p="y"===l?bottom:right,g=s.rects.reference[u]+s.rects.reference[l]-a[l]-s.rects.popper[u],f=a[l]-s.rects.reference[l],E=getOffsetParent(r),_=E?"y"===l?E.clientHeight||0:E.clientWidth||0:0,m=g/2-f/2,v=c[d],T=_-h[u]-c[p],A=_/2-h[u]/2+m,b=within(v,A,T),C=l;s.modifiersData[i]=((t={})[C]=b,t.centerOffset=b-A,t)}}function effect$1(e){var t=e.state,s=e.options.element,i=void 0===s?"[data-popper-arrow]":s;null!=i&&("string"!=typeof i||(i=t.elements.popper.querySelector(i)))&&contains(t.elements.popper,i)&&(t.elements.arrow=i)}var arrow$1={name:"arrow",enabled:!0,phase:"main",fn:arrow,effect:effect$1,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function getVariation(e){return e.split("-")[1]}var unsetSides={top:"auto",right:"auto",bottom:"auto",left:"auto"};function roundOffsetsByDPR(e,t){var s=e.x,i=e.y,n=t.devicePixelRatio||1;return{x:round(s*n)/n||0,y:round(i*n)/n||0}}function mapToStyles(e){var t,s=e.popper,i=e.popperRect,n=e.placement,r=e.variation,a=e.offsets,o=e.position,l=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,h=e.isFixed,d=a.x,p=void 0===d?0:d,g=a.y,f=void 0===g?0:g,E="function"==typeof c?c({x:p,y:f}):{x:p,y:f};p=E.x,f=E.y;var _=a.hasOwnProperty("x"),m=a.hasOwnProperty("y"),v=left,T=top,A=window;if(u){var b=getOffsetParent(s),C="clientHeight",S="clientWidth";if(b===getWindow(s)&&"static"!==getComputedStyle$1(b=getDocumentElement(s)).position&&"absolute"===o&&(C="scrollHeight",S="scrollWidth"),n===top||(n===left||n===right)&&r===end)T=bottom,f-=(h&&b===A&&A.visualViewport?A.visualViewport.height:b[C])-i.height,f*=l?1:-1;if(n===left||(n===top||n===bottom)&&r===end)v=right,p-=(h&&b===A&&A.visualViewport?A.visualViewport.width:b[S])-i.width,p*=l?1:-1}var D,k=Object.assign({position:o},u&&unsetSides),N=!0===c?roundOffsetsByDPR({x:p,y:f},getWindow(s)):{x:p,y:f};return p=N.x,f=N.y,l?Object.assign({},k,((D={})[T]=m?"0":"",D[v]=_?"0":"",D.transform=(A.devicePixelRatio||1)<=1?"translate("+p+"px, "+f+"px)":"translate3d("+p+"px, "+f+"px, 0)",D)):Object.assign({},k,((t={})[T]=m?f+"px":"",t[v]=_?p+"px":"",t.transform="",t))}function computeStyles(e){var t=e.state,s=e.options,i=s.gpuAcceleration,n=void 0===i||i,r=s.adaptive,a=void 0===r||r,o=s.roundOffsets,l=void 0===o||o,u={placement:getBasePlacement(t.placement),variation:getVariation(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:n,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,mapToStyles(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,mapToStyles(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})}var computeStyles$1={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:computeStyles,data:{}},passive={passive:!0};function effect(e){var t=e.state,s=e.instance,i=e.options,n=i.scroll,r=void 0===n||n,a=i.resize,o=void 0===a||a,l=getWindow(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return r&&u.forEach((function(e){e.addEventListener("scroll",s.update,passive)})),o&&l.addEventListener("resize",s.update,passive),function(){r&&u.forEach((function(e){e.removeEventListener("scroll",s.update,passive)})),o&&l.removeEventListener("resize",s.update,passive)}}var eventListeners={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:effect,data:{}},hash$1={left:"right",right:"left",bottom:"top",top:"bottom"};function getOppositePlacement(e){return e.replace(/left|right|bottom|top/g,(function(e){return hash$1[e]}))}var hash={start:"end",end:"start"};function getOppositeVariationPlacement(e){return e.replace(/start|end/g,(function(e){return hash[e]}))}function getWindowScroll(e){var t=getWindow(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function getWindowScrollBarX(e){return getBoundingClientRect(getDocumentElement(e)).left+getWindowScroll(e).scrollLeft}function getViewportRect(e,t){var s=getWindow(e),i=getDocumentElement(e),n=s.visualViewport,r=i.clientWidth,a=i.clientHeight,o=0,l=0;if(n){r=n.width,a=n.height;var u=isLayoutViewport();(u||!u&&"fixed"===t)&&(o=n.offsetLeft,l=n.offsetTop)}return{width:r,height:a,x:o+getWindowScrollBarX(e),y:l}}function getDocumentRect(e){var t,s=getDocumentElement(e),i=getWindowScroll(e),n=null==(t=e.ownerDocument)?void 0:t.body,r=max(s.scrollWidth,s.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),a=max(s.scrollHeight,s.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),o=-i.scrollLeft+getWindowScrollBarX(e),l=-i.scrollTop;return"rtl"===getComputedStyle$1(n||s).direction&&(o+=max(s.clientWidth,n?n.clientWidth:0)-r),{width:r,height:a,x:o,y:l}}function isScrollParent(e){var t=getComputedStyle$1(e),s=t.overflow,i=t.overflowX,n=t.overflowY;return/auto|scroll|overlay|hidden/.test(s+n+i)}function getScrollParent(e){return["html","body","#document"].indexOf(getNodeName(e))>=0?e.ownerDocument.body:isHTMLElement(e)&&isScrollParent(e)?e:getScrollParent(getParentNode(e))}function listScrollParents(e,t){var s;void 0===t&&(t=[]);var i=getScrollParent(e),n=i===(null==(s=e.ownerDocument)?void 0:s.body),r=getWindow(i),a=n?[r].concat(r.visualViewport||[],isScrollParent(i)?i:[]):i,o=t.concat(a);return n?o:o.concat(listScrollParents(getParentNode(a)))}function rectToClientRect(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function getInnerBoundingClientRect(e,t){var s=getBoundingClientRect(e,!1,"fixed"===t);return s.top=s.top+e.clientTop,s.left=s.left+e.clientLeft,s.bottom=s.top+e.clientHeight,s.right=s.left+e.clientWidth,s.width=e.clientWidth,s.height=e.clientHeight,s.x=s.left,s.y=s.top,s}function getClientRectFromMixedType(e,t,s){return t===viewport?rectToClientRect(getViewportRect(e,s)):isElement$1(t)?getInnerBoundingClientRect(t,s):rectToClientRect(getDocumentRect(getDocumentElement(e)))}function getClippingParents(e){var t=listScrollParents(getParentNode(e)),s=["absolute","fixed"].indexOf(getComputedStyle$1(e).position)>=0&&isHTMLElement(e)?getOffsetParent(e):e;return isElement$1(s)?t.filter((function(e){return isElement$1(e)&&contains(e,s)&&"body"!==getNodeName(e)})):[]}function getClippingRect(e,t,s,i){var n="clippingParents"===t?getClippingParents(e):[].concat(t),r=[].concat(n,[s]),a=r[0],o=r.reduce((function(t,s){var n=getClientRectFromMixedType(e,s,i);return t.top=max(n.top,t.top),t.right=min(n.right,t.right),t.bottom=min(n.bottom,t.bottom),t.left=max(n.left,t.left),t}),getClientRectFromMixedType(e,a,i));return o.width=o.right-o.left,o.height=o.bottom-o.top,o.x=o.left,o.y=o.top,o}function computeOffsets(e){var t,s=e.reference,i=e.element,n=e.placement,r=n?getBasePlacement(n):null,a=n?getVariation(n):null,o=s.x+s.width/2-i.width/2,l=s.y+s.height/2-i.height/2;switch(r){case top:t={x:o,y:s.y-i.height};break;case bottom:t={x:o,y:s.y+s.height};break;case right:t={x:s.x+s.width,y:l};break;case left:t={x:s.x-i.width,y:l};break;default:t={x:s.x,y:s.y}}var u=r?getMainAxisFromPlacement(r):null;if(null!=u){var c="y"===u?"height":"width";switch(a){case start:t[u]=t[u]-(s[c]/2-i[c]/2);break;case end:t[u]=t[u]+(s[c]/2-i[c]/2)}}return t}function detectOverflow(e,t){void 0===t&&(t={});var s=t,i=s.placement,n=void 0===i?e.placement:i,r=s.strategy,a=void 0===r?e.strategy:r,o=s.boundary,l=void 0===o?clippingParents:o,u=s.rootBoundary,c=void 0===u?viewport:u,h=s.elementContext,d=void 0===h?popper:h,p=s.altBoundary,g=void 0!==p&&p,f=s.padding,E=void 0===f?0:f,_=mergePaddingObject("number"!=typeof E?E:expandToHashMap(E,basePlacements)),m=d===popper?reference:popper,v=e.rects.popper,T=e.elements[g?m:d],A=getClippingRect(isElement$1(T)?T:T.contextElement||getDocumentElement(e.elements.popper),l,c,a),b=getBoundingClientRect(e.elements.reference),C=computeOffsets({reference:b,element:v,strategy:"absolute",placement:n}),S=rectToClientRect(Object.assign({},v,C)),D=d===popper?S:b,k={top:A.top-D.top+_.top,bottom:D.bottom-A.bottom+_.bottom,left:A.left-D.left+_.left,right:D.right-A.right+_.right},N=e.modifiersData.offset;if(d===popper&&N){var O=N[n];Object.keys(k).forEach((function(e){var t=[right,bottom].indexOf(e)>=0?1:-1,s=[top,bottom].indexOf(e)>=0?"y":"x";k[e]+=O[s]*t}))}return k}function computeAutoPlacement(e,t){void 0===t&&(t={});var s=t,i=s.placement,n=s.boundary,r=s.rootBoundary,a=s.padding,o=s.flipVariations,l=s.allowedAutoPlacements,u=void 0===l?placements:l,c=getVariation(i),h=c?o?variationPlacements:variationPlacements.filter((function(e){return getVariation(e)===c})):basePlacements,d=h.filter((function(e){return u.indexOf(e)>=0}));0===d.length&&(d=h);var p=d.reduce((function(t,s){return t[s]=detectOverflow(e,{placement:s,boundary:n,rootBoundary:r,padding:a})[getBasePlacement(s)],t}),{});return Object.keys(p).sort((function(e,t){return p[e]-p[t]}))}function getExpandedFallbackPlacements(e){if(getBasePlacement(e)===auto)return[];var t=getOppositePlacement(e);return[getOppositeVariationPlacement(e),t,getOppositeVariationPlacement(t)]}function flip(e){var t=e.state,s=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var n=s.mainAxis,r=void 0===n||n,a=s.altAxis,o=void 0===a||a,l=s.fallbackPlacements,u=s.padding,c=s.boundary,h=s.rootBoundary,d=s.altBoundary,p=s.flipVariations,g=void 0===p||p,f=s.allowedAutoPlacements,E=t.options.placement,_=getBasePlacement(E),m=l||(_===E||!g?[getOppositePlacement(E)]:getExpandedFallbackPlacements(E)),v=[E].concat(m).reduce((function(e,s){return e.concat(getBasePlacement(s)===auto?computeAutoPlacement(t,{placement:s,boundary:c,rootBoundary:h,padding:u,flipVariations:g,allowedAutoPlacements:f}):s)}),[]),T=t.rects.reference,A=t.rects.popper,b=new Map,C=!0,S=v[0],D=0;D<v.length;D++){var k=v[D],N=getBasePlacement(k),O=getVariation(k)===start,y=[top,bottom].indexOf(N)>=0,w=y?"width":"height",I=detectOverflow(t,{placement:k,boundary:c,rootBoundary:h,altBoundary:d,padding:u}),M=y?O?right:left:O?bottom:top;T[w]>A[w]&&(M=getOppositePlacement(M));var L=getOppositePlacement(M),R=[];if(r&&R.push(I[N]<=0),o&&R.push(I[M]<=0,I[L]<=0),R.every((function(e){return e}))){S=k,C=!1;break}b.set(k,R)}if(C)for(var P=function(e){var t=v.find((function(t){var s=b.get(t);if(s)return s.slice(0,e).every((function(e){return e}))}));if(t)return S=t,"break"},$=g?3:1;$>0;$--){if("break"===P($))break}t.placement!==S&&(t.modifiersData[i]._skip=!0,t.placement=S,t.reset=!0)}}var flip$1={name:"flip",enabled:!0,phase:"main",fn:flip,requiresIfExists:["offset"],data:{_skip:!1}};function getSideOffsets(e,t,s){return void 0===s&&(s={x:0,y:0}),{top:e.top-t.height-s.y,right:e.right-t.width+s.x,bottom:e.bottom-t.height+s.y,left:e.left-t.width-s.x}}function isAnySideFullyClipped(e){return[top,right,bottom,left].some((function(t){return e[t]>=0}))}function hide(e){var t=e.state,s=e.name,i=t.rects.reference,n=t.rects.popper,r=t.modifiersData.preventOverflow,a=detectOverflow(t,{elementContext:"reference"}),o=detectOverflow(t,{altBoundary:!0}),l=getSideOffsets(a,i),u=getSideOffsets(o,n,r),c=isAnySideFullyClipped(l),h=isAnySideFullyClipped(u);t.modifiersData[s]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":h})}var hide$1={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:hide};function distanceAndSkiddingToXY(e,t,s){var i=getBasePlacement(e),n=[left,top].indexOf(i)>=0?-1:1,r="function"==typeof s?s(Object.assign({},t,{placement:e})):s,a=r[0],o=r[1];return a=a||0,o=(o||0)*n,[left,right].indexOf(i)>=0?{x:o,y:a}:{x:a,y:o}}function offset(e){var t=e.state,s=e.options,i=e.name,n=s.offset,r=void 0===n?[0,0]:n,a=placements.reduce((function(e,s){return e[s]=distanceAndSkiddingToXY(s,t.rects,r),e}),{}),o=a[t.placement],l=o.x,u=o.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[i]=a}var offset$1={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:offset};function popperOffsets(e){var t=e.state,s=e.name;t.modifiersData[s]=computeOffsets({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})}var popperOffsets$1={name:"popperOffsets",enabled:!0,phase:"read",fn:popperOffsets,data:{}};function getAltAxis(e){return"x"===e?"y":"x"}function preventOverflow(e){var t=e.state,s=e.options,i=e.name,n=s.mainAxis,r=void 0===n||n,a=s.altAxis,o=void 0!==a&&a,l=s.boundary,u=s.rootBoundary,c=s.altBoundary,h=s.padding,d=s.tether,p=void 0===d||d,g=s.tetherOffset,f=void 0===g?0:g,E=detectOverflow(t,{boundary:l,rootBoundary:u,padding:h,altBoundary:c}),_=getBasePlacement(t.placement),m=getVariation(t.placement),v=!m,T=getMainAxisFromPlacement(_),A=getAltAxis(T),b=t.modifiersData.popperOffsets,C=t.rects.reference,S=t.rects.popper,D="function"==typeof f?f(Object.assign({},t.rects,{placement:t.placement})):f,k="number"==typeof D?{mainAxis:D,altAxis:D}:Object.assign({mainAxis:0,altAxis:0},D),N=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,O={x:0,y:0};if(b){if(r){var y,w="y"===T?top:left,I="y"===T?bottom:right,M="y"===T?"height":"width",L=b[T],R=L+E[w],P=L-E[I],$=p?-S[M]/2:0,F=m===start?C[M]:S[M],x=m===start?-S[M]:-C[M],V=t.elements.arrow,H=p&&V?getLayoutRect(V):{width:0,height:0},B=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:getFreshSideObject(),W=B[w],K=B[I],j=within(0,C[M],H[M]),Y=v?C[M]/2-$-j-W-k.mainAxis:F-j-W-k.mainAxis,U=v?-C[M]/2+$+j+K+k.mainAxis:x+j+K+k.mainAxis,G=t.elements.arrow&&getOffsetParent(t.elements.arrow),z=G?"y"===T?G.clientTop||0:G.clientLeft||0:0,q=null!=(y=null==N?void 0:N[T])?y:0,X=L+U-q,Q=within(p?min(R,L+Y-q-z):R,L,p?max(P,X):P);b[T]=Q,O[T]=Q-L}if(o){var Z,J="x"===T?top:left,ee="x"===T?bottom:right,te=b[A],se="y"===A?"height":"width",ie=te+E[J],ne=te-E[ee],re=-1!==[top,left].indexOf(_),ae=null!=(Z=null==N?void 0:N[A])?Z:0,oe=re?ie:te-C[se]-S[se]-ae+k.altAxis,le=re?te+C[se]+S[se]-ae-k.altAxis:ne,ue=p&&re?withinMaxClamp(oe,te,le):within(p?oe:ie,te,p?le:ne);b[A]=ue,O[A]=ue-te}t.modifiersData[i]=O}}var preventOverflow$1={name:"preventOverflow",enabled:!0,phase:"main",fn:preventOverflow,requiresIfExists:["offset"]};function getHTMLElementScroll(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}function getNodeScroll(e){return e!==getWindow(e)&&isHTMLElement(e)?getHTMLElementScroll(e):getWindowScroll(e)}function isElementScaled(e){var t=e.getBoundingClientRect(),s=round(t.width)/e.offsetWidth||1,i=round(t.height)/e.offsetHeight||1;return 1!==s||1!==i}function getCompositeRect(e,t,s){void 0===s&&(s=!1);var i=isHTMLElement(t),n=isHTMLElement(t)&&isElementScaled(t),r=getDocumentElement(t),a=getBoundingClientRect(e,n,s),o={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(i||!i&&!s)&&(("body"!==getNodeName(t)||isScrollParent(r))&&(o=getNodeScroll(t)),isHTMLElement(t)?((l=getBoundingClientRect(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):r&&(l.x=getWindowScrollBarX(r))),{x:a.left+o.scrollLeft-l.x,y:a.top+o.scrollTop-l.y,width:a.width,height:a.height}}function order(e){var t=new Map,s=new Set,i=[];function n(e){s.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!s.has(e)){var i=t.get(e);i&&n(i)}})),i.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){s.has(e.name)||n(e)})),i}function orderModifiers(e){var t=order(e);return modifierPhases.reduce((function(e,s){return e.concat(t.filter((function(e){return e.phase===s})))}),[])}function debounce(e){var t;return function(){return t||(t=new Promise((function(s){Promise.resolve().then((function(){t=void 0,s(e())}))}))),t}}function mergeByName(e){var t=e.reduce((function(e,t){var s=e[t.name];return e[t.name]=s?Object.assign({},s,t,{options:Object.assign({},s.options,t.options),data:Object.assign({},s.data,t.data)}):t,e}),{});return Object.keys(t).map((function(e){return t[e]}))}var DEFAULT_OPTIONS={placement:"bottom",modifiers:[],strategy:"absolute"};function areValidElements(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function popperGenerator(e){void 0===e&&(e={});var t=e,s=t.defaultModifiers,i=void 0===s?[]:s,n=t.defaultOptions,r=void 0===n?DEFAULT_OPTIONS:n;return function(e,t,s){void 0===s&&(s=r);var n={placement:"bottom",orderedModifiers:[],options:Object.assign({},DEFAULT_OPTIONS,r),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},a=[],o=!1,l={state:n,setOptions:function(s){var o="function"==typeof s?s(n.options):s;u(),n.options=Object.assign({},r,n.options,o),n.scrollParents={reference:isElement$1(e)?listScrollParents(e):e.contextElement?listScrollParents(e.contextElement):[],popper:listScrollParents(t)};var c=orderModifiers(mergeByName([].concat(i,n.options.modifiers)));return n.orderedModifiers=c.filter((function(e){return e.enabled})),n.orderedModifiers.forEach((function(e){var t=e.name,s=e.options,i=void 0===s?{}:s,r=e.effect;if("function"==typeof r){var o=r({state:n,name:t,instance:l,options:i}),u=function(){};a.push(o||u)}})),l.update()},forceUpdate:function(){if(!o){var e=n.elements,t=e.reference,s=e.popper;if(areValidElements(t,s)){n.rects={reference:getCompositeRect(t,getOffsetParent(s),"fixed"===n.options.strategy),popper:getLayoutRect(s)},n.reset=!1,n.placement=n.options.placement,n.orderedModifiers.forEach((function(e){return n.modifiersData[e.name]=Object.assign({},e.data)}));for(var i=0;i<n.orderedModifiers.length;i++)if(!0!==n.reset){var r=n.orderedModifiers[i],a=r.fn,u=r.options,c=void 0===u?{}:u,h=r.name;"function"==typeof a&&(n=a({state:n,options:c,name:h,instance:l})||n)}else n.reset=!1,i=-1}}},update:debounce((function(){return new Promise((function(e){l.forceUpdate(),e(n)}))})),destroy:function(){u(),o=!0}};if(!areValidElements(e,t))return l;function u(){a.forEach((function(e){return e()})),a=[]}return l.setOptions(s).then((function(e){!o&&s.onFirstUpdate&&s.onFirstUpdate(e)})),l}}var createPopper$2=popperGenerator(),defaultModifiers$1=[eventListeners,popperOffsets$1,computeStyles$1,applyStyles$1],createPopper$1=popperGenerator({defaultModifiers:defaultModifiers$1}),defaultModifiers=[eventListeners,popperOffsets$1,computeStyles$1,applyStyles$1,offset$1,flip$1,preventOverflow$1,arrow$1,hide$1],createPopper=popperGenerator({defaultModifiers:defaultModifiers}),Popper=Object.freeze({__proto__:null,popperGenerator:popperGenerator,detectOverflow:detectOverflow,createPopperBase:createPopper$2,createPopper:createPopper,createPopperLite:createPopper$1,top:top,bottom:bottom,right:right,left:left,auto:auto,basePlacements:basePlacements,start:start,end:end,clippingParents:clippingParents,viewport:viewport,popper:popper,reference:reference,variationPlacements:variationPlacements,placements:placements,beforeRead:beforeRead,read:read,afterRead:afterRead,beforeMain:beforeMain,main:main,afterMain:afterMain,beforeWrite:beforeWrite,write:write,afterWrite:afterWrite,modifierPhases:modifierPhases,applyStyles:applyStyles$1,arrow:arrow$1,computeStyles:computeStyles$1,eventListeners:eventListeners,flip:flip$1,hide:hide$1,offset:offset$1,popperOffsets:popperOffsets$1,preventOverflow:preventOverflow$1});
/*!
  * Bootstrap v5.3.0-alpha3 (https://getbootstrap.com/)
  * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
const elementMap=new Map,Data={set(e,t,s){elementMap.has(e)||elementMap.set(e,new Map);const i=elementMap.get(e);i.has(t)||0===i.size?i.set(t,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`)},get:(e,t)=>elementMap.has(e)&&elementMap.get(e).get(t)||null,remove(e,t){if(!elementMap.has(e))return;const s=elementMap.get(e);s.delete(t),0===s.size&&elementMap.delete(e)}},MAX_UID=1e6,MILLISECONDS_MULTIPLIER=1e3,TRANSITION_END="transitionend",parseSelector=e=>(e&&window.CSS&&window.CSS.escape&&(e=e.replace(/#([^\s"#']+)/g,((e,t)=>`#${CSS.escape(t)}`))),e),toType=e=>null==e?`${e}`:Object.prototype.toString.call(e).match(/\s([a-z]+)/i)[1].toLowerCase(),getUID=e=>{do{e+=Math.floor(1e6*Math.random())}while(document.getElementById(e));return e},getTransitionDurationFromElement=e=>{if(!e)return 0;let{transitionDuration:t,transitionDelay:s}=window.getComputedStyle(e);const i=Number.parseFloat(t),n=Number.parseFloat(s);return i||n?(t=t.split(",")[0],s=s.split(",")[0],1e3*(Number.parseFloat(t)+Number.parseFloat(s))):0},triggerTransitionEnd=e=>{e.dispatchEvent(new Event(TRANSITION_END))},isElement=e=>!(!e||"object"!=typeof e)&&(void 0!==e.jquery&&(e=e[0]),void 0!==e.nodeType),getElement=e=>isElement(e)?e.jquery?e[0]:e:"string"==typeof e&&e.length>0?document.querySelector(parseSelector(e)):null,isVisible=e=>{if(!isElement(e)||0===e.getClientRects().length)return!1;const t="visible"===getComputedStyle(e).getPropertyValue("visibility"),s=e.closest("details:not([open])");if(!s)return t;if(s!==e){const t=e.closest("summary");if(t&&t.parentNode!==s)return!1;if(null===t)return!1}return t},isDisabled=e=>!e||e.nodeType!==Node.ELEMENT_NODE||(!!e.classList.contains("disabled")||(void 0!==e.disabled?e.disabled:e.hasAttribute("disabled")&&"false"!==e.getAttribute("disabled"))),findShadowRoot=e=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof e.getRootNode){const t=e.getRootNode();return t instanceof ShadowRoot?t:null}return e instanceof ShadowRoot?e:e.parentNode?findShadowRoot(e.parentNode):null},noop=()=>{},reflow=e=>{e.offsetHeight},getjQuery=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,DOMContentLoadedCallbacks=[],onDOMContentLoaded=e=>{"loading"===document.readyState?(DOMContentLoadedCallbacks.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of DOMContentLoadedCallbacks)e()})),DOMContentLoadedCallbacks.push(e)):e()},isRTL=()=>"rtl"===document.documentElement.dir,defineJQueryPlugin=e=>{var t;t=()=>{const t=getjQuery();if(t){const s=e.NAME,i=t.fn[s];t.fn[s]=e.jQueryInterface,t.fn[s].Constructor=e,t.fn[s].noConflict=()=>(t.fn[s]=i,e.jQueryInterface)}},"loading"===document.readyState?(DOMContentLoadedCallbacks.length||document.addEventListener("DOMContentLoaded",(()=>{for(const e of DOMContentLoadedCallbacks)e()})),DOMContentLoadedCallbacks.push(t)):t()},execute=(e,t=[],s=e)=>"function"==typeof e?e(...t):s,executeAfterTransition=(e,t,s=!0)=>{if(!s)return void execute(e);const i=getTransitionDurationFromElement(t)+5;let n=!1;const r=({target:s})=>{s===t&&(n=!0,t.removeEventListener(TRANSITION_END,r),execute(e))};t.addEventListener(TRANSITION_END,r),setTimeout((()=>{n||triggerTransitionEnd(t)}),i)},getNextActiveElement=(e,t,s,i)=>{const n=e.length;let r=e.indexOf(t);return-1===r?!s&&i?e[n-1]:e[0]:(r+=s?1:-1,i&&(r=(r+n)%n),e[Math.max(0,Math.min(r,n-1))])},namespaceRegex=/[^.]*(?=\..*)\.|.*/,stripNameRegex=/\..*/,stripUidRegex=/::\d+$/,eventRegistry={};let uidEvent=1;const customEvents={mouseenter:"mouseover",mouseleave:"mouseout"},nativeEvents=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function makeEventUid(e,t){return t&&`${t}::${uidEvent++}`||e.uidEvent||uidEvent++}function getElementEvents(e){const t=makeEventUid(e);return e.uidEvent=t,eventRegistry[t]=eventRegistry[t]||{},eventRegistry[t]}function bootstrapHandler(e,t){return function s(i){return hydrateObj(i,{delegateTarget:e}),s.oneOff&&EventHandler.off(e,i.type,t),t.apply(e,[i])}}function bootstrapDelegationHandler(e,t,s){return function i(n){const r=e.querySelectorAll(t);for(let{target:a}=n;a&&a!==this;a=a.parentNode)for(const o of r)if(o===a)return hydrateObj(n,{delegateTarget:a}),i.oneOff&&EventHandler.off(e,n.type,t,s),s.apply(a,[n])}}function findHandler(e,t,s=null){return Object.values(e).find((e=>e.callable===t&&e.delegationSelector===s))}function normalizeParameters(e,t,s){const i="string"==typeof t,n=i?s:t||s;let r=getTypeEvent(e);return nativeEvents.has(r)||(r=e),[i,n,r]}function addHandler(e,t,s,i,n){if("string"!=typeof t||!e)return;let[r,a,o]=normalizeParameters(t,s,i);if(t in customEvents){const e=e=>function(t){if(!t.relatedTarget||t.relatedTarget!==t.delegateTarget&&!t.delegateTarget.contains(t.relatedTarget))return e.call(this,t)};a=e(a)}const l=getElementEvents(e),u=l[o]||(l[o]={}),c=findHandler(u,a,r?s:null);if(c)return void(c.oneOff=c.oneOff&&n);const h=makeEventUid(a,t.replace(namespaceRegex,"")),d=r?bootstrapDelegationHandler(e,s,a):bootstrapHandler(e,a);d.delegationSelector=r?s:null,d.callable=a,d.oneOff=n,d.uidEvent=h,u[h]=d,e.addEventListener(o,d,r)}function removeHandler(e,t,s,i,n){const r=findHandler(t[s],i,n);r&&(e.removeEventListener(s,r,Boolean(n)),delete t[s][r.uidEvent])}function removeNamespacedHandlers(e,t,s,i){const n=t[s]||{};for(const[r,a]of Object.entries(n))r.includes(i)&&removeHandler(e,t,s,a.callable,a.delegationSelector)}function getTypeEvent(e){return e=e.replace(stripNameRegex,""),customEvents[e]||e}const EventHandler={on(e,t,s,i){addHandler(e,t,s,i,!1)},one(e,t,s,i){addHandler(e,t,s,i,!0)},off(e,t,s,i){if("string"!=typeof t||!e)return;const[n,r,a]=normalizeParameters(t,s,i),o=a!==t,l=getElementEvents(e),u=l[a]||{},c=t.startsWith(".");if(void 0===r){if(c)for(const s of Object.keys(l))removeNamespacedHandlers(e,l,s,t.slice(1));for(const[s,i]of Object.entries(u)){const n=s.replace(stripUidRegex,"");o&&!t.includes(n)||removeHandler(e,l,a,i.callable,i.delegationSelector)}}else{if(!Object.keys(u).length)return;removeHandler(e,l,a,r,n?s:null)}},trigger(e,t,s){if("string"!=typeof t||!e)return null;const i=getjQuery();let n=null,r=!0,a=!0,o=!1;t!==getTypeEvent(t)&&i&&(n=i.Event(t,s),i(e).trigger(n),r=!n.isPropagationStopped(),a=!n.isImmediatePropagationStopped(),o=n.isDefaultPrevented());const l=hydrateObj(new Event(t,{bubbles:r,cancelable:!0}),s);return o&&l.preventDefault(),a&&e.dispatchEvent(l),l.defaultPrevented&&n&&n.preventDefault(),l}};function hydrateObj(e,t={}){for(const[s,i]of Object.entries(t))try{e[s]=i}catch(t){Object.defineProperty(e,s,{configurable:!0,get:()=>i})}return e}function normalizeData(e){if("true"===e)return!0;if("false"===e)return!1;if(e===Number(e).toString())return Number(e);if(""===e||"null"===e)return null;if("string"!=typeof e)return e;try{return JSON.parse(decodeURIComponent(e))}catch(t){return e}}function normalizeDataKey(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`))}const Manipulator={setDataAttribute(e,t,s){e.setAttribute(`data-bs-${normalizeDataKey(t)}`,s)},removeDataAttribute(e,t){e.removeAttribute(`data-bs-${normalizeDataKey(t)}`)},getDataAttributes(e){if(!e)return{};const t={},s=Object.keys(e.dataset).filter((e=>e.startsWith("bs")&&!e.startsWith("bsConfig")));for(const i of s){let s=i.replace(/^bs/,"");s=s.charAt(0).toLowerCase()+s.slice(1,s.length),t[s]=normalizeData(e.dataset[i])}return t},getDataAttribute:(e,t)=>normalizeData(e.getAttribute(`data-bs-${normalizeDataKey(t)}`))};class Config{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(e){return e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e}_mergeConfigObj(e,t){const s=isElement(t)?Manipulator.getDataAttribute(t,"config"):{};return{...this.constructor.Default,..."object"==typeof s?s:{},...isElement(t)?Manipulator.getDataAttributes(t):{},..."object"==typeof e?e:{}}}_typeCheckConfig(e,t=this.constructor.DefaultType){for(const[i,n]of Object.entries(t)){const t=e[i],r=isElement(t)?"element":null==(s=t)?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(n).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${r}" but expected type "${n}".`)}var s}}const VERSION="5.3.0-alpha2";class BaseComponent extends Config{constructor(e,t){super(),(e=getElement(e))&&(this._element=e,this._config=this._getConfig(t),Data.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Data.remove(this._element,this.constructor.DATA_KEY),EventHandler.off(this._element,this.constructor.EVENT_KEY);for(const e of Object.getOwnPropertyNames(this))this[e]=null}_queueCallback(e,t,s=!0){executeAfterTransition(e,t,s)}_getConfig(e){return e=this._mergeConfigObj(e,this._element),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}static getInstance(e){return Data.get(getElement(e),this.DATA_KEY)}static getOrCreateInstance(e,t={}){return this.getInstance(e)||new this(e,"object"==typeof t?t:null)}static get VERSION(){return VERSION}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(e){return`${e}${this.EVENT_KEY}`}}const getSelector=e=>{let t=e.getAttribute("data-bs-target");if(!t||"#"===t){let s=e.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),t=s&&"#"!==s?s.trim():null}return parseSelector(t)},SelectorEngine={find:(e,t=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(t,e)),findOne:(e,t=document.documentElement)=>Element.prototype.querySelector.call(t,e),children:(e,t)=>[].concat(...e.children).filter((e=>e.matches(t))),parents(e,t){const s=[];let i=e.parentNode.closest(t);for(;i;)s.push(i),i=i.parentNode.closest(t);return s},prev(e,t){let s=e.previousElementSibling;for(;s;){if(s.matches(t))return[s];s=s.previousElementSibling}return[]},next(e,t){let s=e.nextElementSibling;for(;s;){if(s.matches(t))return[s];s=s.nextElementSibling}return[]},focusableChildren(e){const t=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((e=>`${e}:not([tabindex^="-"])`)).join(",");return this.find(t,e).filter((e=>!isDisabled(e)&&isVisible(e)))},getSelectorFromElement(e){const t=getSelector(e);return t&&SelectorEngine.findOne(t)?t:null},getElementFromSelector(e){const t=getSelector(e);return t?SelectorEngine.findOne(t):null},getMultipleElementsFromSelector(e){const t=getSelector(e);return t?SelectorEngine.find(t):[]}},enableDismissTrigger=(e,t="hide")=>{const s=`click.dismiss${e.EVENT_KEY}`,i=e.NAME;EventHandler.on(document,s,`[data-bs-dismiss="${i}"]`,(function(s){if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),isDisabled(this))return;const n=SelectorEngine.getElementFromSelector(this)||this.closest(`.${i}`);e.getOrCreateInstance(n)[t]()}))},NAME$f="alert",DATA_KEY$a="bs.alert",EVENT_KEY$b=".bs.alert",EVENT_CLOSE="close.bs.alert",EVENT_CLOSED="closed.bs.alert",CLASS_NAME_FADE$5="fade",CLASS_NAME_SHOW$8="show";class Alert extends BaseComponent{static get NAME(){return NAME$f}close(){if(EventHandler.trigger(this._element,EVENT_CLOSE).defaultPrevented)return;this._element.classList.remove("show");const e=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,e)}_destroyElement(){this._element.remove(),EventHandler.trigger(this._element,EVENT_CLOSED),this.dispose()}static jQueryInterface(e){return this.each((function(){const t=Alert.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}enableDismissTrigger(Alert,"close"),defineJQueryPlugin(Alert);const NAME$e="button",DATA_KEY$9="bs.button",EVENT_KEY$a=`.${DATA_KEY$9}`,DATA_API_KEY$6=".data-api",CLASS_NAME_ACTIVE$3="active",SELECTOR_DATA_TOGGLE$5='[data-bs-toggle="button"]',EVENT_CLICK_DATA_API$6=`click${EVENT_KEY$a}.data-api`;class Button extends BaseComponent{static get NAME(){return NAME$e}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(e){return this.each((function(){const t=Button.getOrCreateInstance(this);"toggle"===e&&t[e]()}))}}EventHandler.on(document,EVENT_CLICK_DATA_API$6,SELECTOR_DATA_TOGGLE$5,(e=>{e.preventDefault();const t=e.target.closest(SELECTOR_DATA_TOGGLE$5);Button.getOrCreateInstance(t).toggle()})),defineJQueryPlugin(Button);const NAME$d="swipe",EVENT_KEY$9=".bs.swipe",EVENT_TOUCHSTART="touchstart.bs.swipe",EVENT_TOUCHMOVE="touchmove.bs.swipe",EVENT_TOUCHEND="touchend.bs.swipe",EVENT_POINTERDOWN="pointerdown.bs.swipe",EVENT_POINTERUP="pointerup.bs.swipe",POINTER_TYPE_TOUCH="touch",POINTER_TYPE_PEN="pen",CLASS_NAME_POINTER_EVENT="pointer-event",SWIPE_THRESHOLD=40,Default$c={endCallback:null,leftCallback:null,rightCallback:null},DefaultType$c={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Swipe extends Config{constructor(e,t){super(),this._element=e,e&&Swipe.isSupported()&&(this._config=this._getConfig(t),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Default$c}static get DefaultType(){return DefaultType$c}static get NAME(){return NAME$d}dispose(){EventHandler.off(this._element,".bs.swipe")}_start(e){this._supportPointerEvents?this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX):this._deltaX=e.touches[0].clientX}_end(e){this._eventIsPointerPenTouch(e)&&(this._deltaX=e.clientX-this._deltaX),this._handleSwipe(),execute(this._config.endCallback)}_move(e){this._deltaX=e.touches&&e.touches.length>1?0:e.touches[0].clientX-this._deltaX}_handleSwipe(){const e=Math.abs(this._deltaX);if(e<=40)return;const t=e/this._deltaX;this._deltaX=0,t&&execute(t>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(EventHandler.on(this._element,EVENT_POINTERDOWN,(e=>this._start(e))),EventHandler.on(this._element,EVENT_POINTERUP,(e=>this._end(e))),this._element.classList.add("pointer-event")):(EventHandler.on(this._element,EVENT_TOUCHSTART,(e=>this._start(e))),EventHandler.on(this._element,EVENT_TOUCHMOVE,(e=>this._move(e))),EventHandler.on(this._element,EVENT_TOUCHEND,(e=>this._end(e))))}_eventIsPointerPenTouch(e){return this._supportPointerEvents&&("pen"===e.pointerType||"touch"===e.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const NAME$c="carousel",DATA_KEY$8="bs.carousel",EVENT_KEY$8=`.${DATA_KEY$8}`,DATA_API_KEY$5=".data-api",ARROW_LEFT_KEY$1="ArrowLeft",ARROW_RIGHT_KEY$1="ArrowRight",TOUCHEVENT_COMPAT_WAIT=500,ORDER_NEXT="next",ORDER_PREV="prev",DIRECTION_LEFT="left",DIRECTION_RIGHT="right",EVENT_SLIDE=`slide${EVENT_KEY$8}`,EVENT_SLID=`slid${EVENT_KEY$8}`,EVENT_KEYDOWN$1=`keydown${EVENT_KEY$8}`,EVENT_MOUSEENTER$1=`mouseenter${EVENT_KEY$8}`,EVENT_MOUSELEAVE$1=`mouseleave${EVENT_KEY$8}`,EVENT_DRAG_START=`dragstart${EVENT_KEY$8}`,EVENT_LOAD_DATA_API$3=`load${EVENT_KEY$8}.data-api`,EVENT_CLICK_DATA_API$5=`click${EVENT_KEY$8}.data-api`,CLASS_NAME_CAROUSEL="carousel",CLASS_NAME_ACTIVE$2="active",CLASS_NAME_SLIDE="slide",CLASS_NAME_END="carousel-item-end",CLASS_NAME_START="carousel-item-start",CLASS_NAME_NEXT="carousel-item-next",CLASS_NAME_PREV="carousel-item-prev",SELECTOR_ACTIVE=".active",SELECTOR_ITEM=".carousel-item",SELECTOR_ACTIVE_ITEM=".active.carousel-item",SELECTOR_ITEM_IMG=".carousel-item img",SELECTOR_INDICATORS=".carousel-indicators",SELECTOR_DATA_SLIDE="[data-bs-slide], [data-bs-slide-to]",SELECTOR_DATA_RIDE='[data-bs-ride="carousel"]',KEY_TO_DIRECTION={ArrowLeft:"right",ArrowRight:"left"},Default$b={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},DefaultType$b={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Carousel extends BaseComponent{constructor(e,t){super(e,t),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=SelectorEngine.findOne(SELECTOR_INDICATORS,this._element),this._addEventListeners(),"carousel"===this._config.ride&&this.cycle()}static get Default(){return Default$b}static get DefaultType(){return DefaultType$b}static get NAME(){return NAME$c}next(){this._slide("next")}nextWhenVisible(){!document.hidden&&isVisible(this._element)&&this.next()}prev(){this._slide("prev")}pause(){this._isSliding&&triggerTransitionEnd(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?EventHandler.one(this._element,EVENT_SLID,(()=>this.cycle())):this.cycle())}to(e){const t=this._getItems();if(e>t.length-1||e<0)return;if(this._isSliding)return void EventHandler.one(this._element,EVENT_SLID,(()=>this.to(e)));const s=this._getItemIndex(this._getActive());if(s===e)return;const i=e>s?"next":"prev";this._slide(i,t[e])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(e){return e.defaultInterval=e.interval,e}_addEventListeners(){this._config.keyboard&&EventHandler.on(this._element,EVENT_KEYDOWN$1,(e=>this._keydown(e))),"hover"===this._config.pause&&(EventHandler.on(this._element,EVENT_MOUSEENTER$1,(()=>this.pause())),EventHandler.on(this._element,EVENT_MOUSELEAVE$1,(()=>this._maybeEnableCycle()))),this._config.touch&&Swipe.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const e of SelectorEngine.find(SELECTOR_ITEM_IMG,this._element))EventHandler.on(e,EVENT_DRAG_START,(e=>e.preventDefault()));const e={leftCallback:()=>this._slide(this._directionToOrder("left")),rightCallback:()=>this._slide(this._directionToOrder("right")),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new Swipe(this._element,e)}_keydown(e){if(/input|textarea/i.test(e.target.tagName))return;const t=KEY_TO_DIRECTION[e.key];t&&(e.preventDefault(),this._slide(this._directionToOrder(t)))}_getItemIndex(e){return this._getItems().indexOf(e)}_setActiveIndicatorElement(e){if(!this._indicatorsElement)return;const t=SelectorEngine.findOne(".active",this._indicatorsElement);t.classList.remove("active"),t.removeAttribute("aria-current");const s=SelectorEngine.findOne(`[data-bs-slide-to="${e}"]`,this._indicatorsElement);s&&(s.classList.add("active"),s.setAttribute("aria-current","true"))}_updateInterval(){const e=this._activeElement||this._getActive();if(!e)return;const t=Number.parseInt(e.getAttribute("data-bs-interval"),10);this._config.interval=t||this._config.defaultInterval}_slide(e,t=null){if(this._isSliding)return;const s=this._getActive(),i="next"===e,n=t||getNextActiveElement(this._getItems(),s,i,this._config.wrap);if(n===s)return;const r=this._getItemIndex(n),a=t=>EventHandler.trigger(this._element,t,{relatedTarget:n,direction:this._orderToDirection(e),from:this._getItemIndex(s),to:r});if(a(EVENT_SLIDE).defaultPrevented)return;if(!s||!n)return;const o=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=n;const l=i?CLASS_NAME_START:CLASS_NAME_END,u=i?CLASS_NAME_NEXT:CLASS_NAME_PREV;n.classList.add(u),reflow(n),s.classList.add(l),n.classList.add(l);this._queueCallback((()=>{n.classList.remove(l,u),n.classList.add("active"),s.classList.remove("active",u,l),this._isSliding=!1,a(EVENT_SLID)}),s,this._isAnimated()),o&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM,this._element)}_getItems(){return SelectorEngine.find(SELECTOR_ITEM,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(e){return isRTL()?"left"===e?"prev":"next":"left"===e?"next":"prev"}_orderToDirection(e){return isRTL()?"prev"===e?"left":"right":"prev"===e?"right":"left"}static jQueryInterface(e){return this.each((function(){const t=Carousel.getOrCreateInstance(this,e);if("number"!=typeof e){if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}else t.to(e)}))}}EventHandler.on(document,EVENT_CLICK_DATA_API$5,SELECTOR_DATA_SLIDE,(function(e){const t=SelectorEngine.getElementFromSelector(this);if(!t||!t.classList.contains("carousel"))return;e.preventDefault();const s=Carousel.getOrCreateInstance(t),i=this.getAttribute("data-bs-slide-to");return i?(s.to(i),void s._maybeEnableCycle()):"next"===Manipulator.getDataAttribute(this,"slide")?(s.next(),void s._maybeEnableCycle()):(s.prev(),void s._maybeEnableCycle())})),EventHandler.on(window,EVENT_LOAD_DATA_API$3,(()=>{const e=SelectorEngine.find(SELECTOR_DATA_RIDE);for(const t of e)Carousel.getOrCreateInstance(t)})),defineJQueryPlugin(Carousel);const NAME$b="collapse",DATA_KEY$7="bs.collapse",EVENT_KEY$7=`.${DATA_KEY$7}`,DATA_API_KEY$4=".data-api",EVENT_SHOW$6=`show${EVENT_KEY$7}`,EVENT_SHOWN$6=`shown${EVENT_KEY$7}`,EVENT_HIDE$6=`hide${EVENT_KEY$7}`,EVENT_HIDDEN$6=`hidden${EVENT_KEY$7}`,EVENT_CLICK_DATA_API$4=`click${EVENT_KEY$7}.data-api`,CLASS_NAME_SHOW$7="show",CLASS_NAME_COLLAPSE="collapse",CLASS_NAME_COLLAPSING="collapsing",CLASS_NAME_COLLAPSED="collapsed",CLASS_NAME_DEEPER_CHILDREN=":scope .collapse .collapse",CLASS_NAME_HORIZONTAL="collapse-horizontal",WIDTH="width",HEIGHT="height",SELECTOR_ACTIVES=".collapse.show, .collapse.collapsing",SELECTOR_DATA_TOGGLE$4='[data-bs-toggle="collapse"]',Default$a={parent:null,toggle:!0},DefaultType$a={parent:"(null|element)",toggle:"boolean"};class Collapse extends BaseComponent{constructor(e,t){super(e,t),this._isTransitioning=!1,this._triggerArray=[];const s=SelectorEngine.find(SELECTOR_DATA_TOGGLE$4);for(const e of s){const t=SelectorEngine.getSelectorFromElement(e),s=SelectorEngine.find(t).filter((e=>e===this._element));null!==t&&s.length&&this._triggerArray.push(e)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Default$a}static get DefaultType(){return DefaultType$a}static get NAME(){return NAME$b}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let e=[];if(this._config.parent&&(e=this._getFirstLevelChildren(SELECTOR_ACTIVES).filter((e=>e!==this._element)).map((e=>Collapse.getOrCreateInstance(e,{toggle:!1})))),e.length&&e[0]._isTransitioning)return;if(EventHandler.trigger(this._element,EVENT_SHOW$6).defaultPrevented)return;for(const t of e)t.hide();const t=this._getDimension();this._element.classList.remove("collapse"),this._element.classList.add("collapsing"),this._element.style[t]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const s=`scroll${t[0].toUpperCase()+t.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove("collapsing"),this._element.classList.add("collapse","show"),this._element.style[t]="",EventHandler.trigger(this._element,EVENT_SHOWN$6)}),this._element,!0),this._element.style[t]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(EventHandler.trigger(this._element,EVENT_HIDE$6).defaultPrevented)return;const e=this._getDimension();this._element.style[e]=`${this._element.getBoundingClientRect()[e]}px`,reflow(this._element),this._element.classList.add("collapsing"),this._element.classList.remove("collapse","show");for(const e of this._triggerArray){const t=SelectorEngine.getElementFromSelector(e);t&&!this._isShown(t)&&this._addAriaAndCollapsedClass([e],!1)}this._isTransitioning=!0;this._element.style[e]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove("collapsing"),this._element.classList.add("collapse"),EventHandler.trigger(this._element,EVENT_HIDDEN$6)}),this._element,!0)}_isShown(e=this._element){return e.classList.contains("show")}_configAfterMerge(e){return e.toggle=Boolean(e.toggle),e.parent=getElement(e.parent),e}_getDimension(){return this._element.classList.contains("collapse-horizontal")?WIDTH:HEIGHT}_initializeChildren(){if(!this._config.parent)return;const e=this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE$4);for(const t of e){const e=SelectorEngine.getElementFromSelector(t);e&&this._addAriaAndCollapsedClass([t],this._isShown(e))}}_getFirstLevelChildren(e){const t=SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN,this._config.parent);return SelectorEngine.find(e,this._config.parent).filter((e=>!t.includes(e)))}_addAriaAndCollapsedClass(e,t){if(e.length)for(const s of e)s.classList.toggle("collapsed",!t),s.setAttribute("aria-expanded",t)}static jQueryInterface(e){const t={};return"string"==typeof e&&/show|hide/.test(e)&&(t.toggle=!1),this.each((function(){const s=Collapse.getOrCreateInstance(this,t);if("string"==typeof e){if(void 0===s[e])throw new TypeError(`No method named "${e}"`);s[e]()}}))}}EventHandler.on(document,EVENT_CLICK_DATA_API$4,SELECTOR_DATA_TOGGLE$4,(function(e){("A"===e.target.tagName||e.delegateTarget&&"A"===e.delegateTarget.tagName)&&e.preventDefault();for(const e of SelectorEngine.getMultipleElementsFromSelector(this))Collapse.getOrCreateInstance(e,{toggle:!1}).toggle()})),defineJQueryPlugin(Collapse);const NAME$a="dropdown",DATA_KEY$6="bs.dropdown",EVENT_KEY$6=`.${DATA_KEY$6}`,DATA_API_KEY$3=".data-api",ESCAPE_KEY$2="Escape",TAB_KEY$1="Tab",ARROW_UP_KEY$1="ArrowUp",ARROW_DOWN_KEY$1="ArrowDown",RIGHT_MOUSE_BUTTON=2,EVENT_HIDE$5=`hide${EVENT_KEY$6}`,EVENT_HIDDEN$5=`hidden${EVENT_KEY$6}`,EVENT_SHOW$5=`show${EVENT_KEY$6}`,EVENT_SHOWN$5=`shown${EVENT_KEY$6}`,EVENT_CLICK_DATA_API$3=`click${EVENT_KEY$6}.data-api`,EVENT_KEYDOWN_DATA_API=`keydown${EVENT_KEY$6}.data-api`,EVENT_KEYUP_DATA_API=`keyup${EVENT_KEY$6}.data-api`,CLASS_NAME_SHOW$6="show",CLASS_NAME_DROPUP="dropup",CLASS_NAME_DROPEND="dropend",CLASS_NAME_DROPSTART="dropstart",CLASS_NAME_DROPUP_CENTER="dropup-center",CLASS_NAME_DROPDOWN_CENTER="dropdown-center",SELECTOR_DATA_TOGGLE$3='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',SELECTOR_DATA_TOGGLE_SHOWN=`${SELECTOR_DATA_TOGGLE$3}.show`,SELECTOR_MENU=".dropdown-menu",SELECTOR_NAVBAR=".navbar",SELECTOR_NAVBAR_NAV=".navbar-nav",SELECTOR_VISIBLE_ITEMS=".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",PLACEMENT_TOP=isRTL()?"top-end":"top-start",PLACEMENT_TOPEND=isRTL()?"top-start":"top-end",PLACEMENT_BOTTOM=isRTL()?"bottom-end":"bottom-start",PLACEMENT_BOTTOMEND=isRTL()?"bottom-start":"bottom-end",PLACEMENT_RIGHT=isRTL()?"left-start":"right-start",PLACEMENT_LEFT=isRTL()?"right-start":"left-start",PLACEMENT_TOPCENTER="top",PLACEMENT_BOTTOMCENTER="bottom",Default$9={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},DefaultType$9={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Dropdown extends BaseComponent{constructor(e,t){super(e,t),this._popper=null,this._parent=this._element.parentNode,this._menu=SelectorEngine.next(this._element,SELECTOR_MENU)[0]||SelectorEngine.prev(this._element,SELECTOR_MENU)[0]||SelectorEngine.findOne(SELECTOR_MENU,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Default$9}static get DefaultType(){return DefaultType$9}static get NAME(){return NAME$a}toggle(){return this._isShown()?this.hide():this.show()}show(){if(isDisabled(this._element)||this._isShown())return;const e={relatedTarget:this._element};if(!EventHandler.trigger(this._element,EVENT_SHOW$5,e).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const e of[].concat(...document.body.children))EventHandler.on(e,"mouseover",noop);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add("show"),this._element.classList.add("show"),EventHandler.trigger(this._element,EVENT_SHOWN$5,e)}}hide(){if(isDisabled(this._element)||!this._isShown())return;const e={relatedTarget:this._element};this._completeHide(e)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(e){if(!EventHandler.trigger(this._element,EVENT_HIDE$5,e).defaultPrevented){if("ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))EventHandler.off(e,"mouseover",noop);this._popper&&this._popper.destroy(),this._menu.classList.remove("show"),this._element.classList.remove("show"),this._element.setAttribute("aria-expanded","false"),Manipulator.removeDataAttribute(this._menu,"popper"),EventHandler.trigger(this._element,EVENT_HIDDEN$5,e)}}_getConfig(e){if("object"==typeof(e=super._getConfig(e)).reference&&!isElement(e.reference)&&"function"!=typeof e.reference.getBoundingClientRect)throw new TypeError(`${NAME$a.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return e}_createPopper(){if(void 0===Popper)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=this._parent:isElement(this._config.reference)?e=getElement(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const t=this._getPopperConfig();this._popper=createPopper(e,this._menu,t)}_isShown(){return this._menu.classList.contains("show")}_getPlacement(){const e=this._parent;if(e.classList.contains("dropend"))return PLACEMENT_RIGHT;if(e.classList.contains("dropstart"))return PLACEMENT_LEFT;if(e.classList.contains("dropup-center"))return"top";if(e.classList.contains("dropdown-center"))return"bottom";const t="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return e.classList.contains("dropup")?t?PLACEMENT_TOPEND:PLACEMENT_TOP:t?PLACEMENT_BOTTOMEND:PLACEMENT_BOTTOM}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_getPopperConfig(){const e={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(Manipulator.setDataAttribute(this._menu,"popper","static"),e.modifiers=[{name:"applyStyles",enabled:!1}]),{...e,...execute(this._config.popperConfig,[e])}}_selectMenuItem({key:e,target:t}){const s=SelectorEngine.find(SELECTOR_VISIBLE_ITEMS,this._menu).filter((e=>isVisible(e)));s.length&&getNextActiveElement(s,t,e===ARROW_DOWN_KEY$1,!s.includes(t)).focus()}static jQueryInterface(e){return this.each((function(){const t=Dropdown.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}static clearMenus(e){if(2===e.button||"keyup"===e.type&&"Tab"!==e.key)return;const t=SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN);for(const s of t){const t=Dropdown.getInstance(s);if(!t||!1===t._config.autoClose)continue;const i=e.composedPath(),n=i.includes(t._menu);if(i.includes(t._element)||"inside"===t._config.autoClose&&!n||"outside"===t._config.autoClose&&n)continue;if(t._menu.contains(e.target)&&("keyup"===e.type&&"Tab"===e.key||/input|select|option|textarea|form/i.test(e.target.tagName)))continue;const r={relatedTarget:t._element};"click"===e.type&&(r.clickEvent=e),t._completeHide(r)}}static dataApiKeydownHandler(e){const t=/input|textarea/i.test(e.target.tagName),s="Escape"===e.key,i=[ARROW_UP_KEY$1,ARROW_DOWN_KEY$1].includes(e.key);if(!i&&!s)return;if(t&&!s)return;e.preventDefault();const n=this.matches(SELECTOR_DATA_TOGGLE$3)?this:SelectorEngine.prev(this,SELECTOR_DATA_TOGGLE$3)[0]||SelectorEngine.next(this,SELECTOR_DATA_TOGGLE$3)[0]||SelectorEngine.findOne(SELECTOR_DATA_TOGGLE$3,e.delegateTarget.parentNode),r=Dropdown.getOrCreateInstance(n);if(i)return e.stopPropagation(),r.show(),void r._selectMenuItem(e);r._isShown()&&(e.stopPropagation(),r.hide(),n.focus())}}EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE$3,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_MENU,Dropdown.dataApiKeydownHandler),EventHandler.on(document,EVENT_CLICK_DATA_API$3,Dropdown.clearMenus),EventHandler.on(document,EVENT_KEYUP_DATA_API,Dropdown.clearMenus),EventHandler.on(document,EVENT_CLICK_DATA_API$3,SELECTOR_DATA_TOGGLE$3,(function(e){e.preventDefault(),Dropdown.getOrCreateInstance(this).toggle()})),defineJQueryPlugin(Dropdown);const NAME$9="backdrop",CLASS_NAME_FADE$4="fade",CLASS_NAME_SHOW$5="show",EVENT_MOUSEDOWN=`mousedown.bs.${NAME$9}`,Default$8={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},DefaultType$8={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Backdrop extends Config{constructor(e){super(),this._config=this._getConfig(e),this._isAppended=!1,this._element=null}static get Default(){return Default$8}static get DefaultType(){return DefaultType$8}static get NAME(){return NAME$9}show(e){if(!this._config.isVisible)return void execute(e);this._append();const t=this._getElement();this._config.isAnimated&&reflow(t),t.classList.add("show"),this._emulateAnimation((()=>{execute(e)}))}hide(e){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation((()=>{this.dispose(),execute(e)}))):execute(e)}dispose(){this._isAppended&&(EventHandler.off(this._element,EVENT_MOUSEDOWN),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const e=document.createElement("div");e.className=this._config.className,this._config.isAnimated&&e.classList.add("fade"),this._element=e}return this._element}_configAfterMerge(e){return e.rootElement=getElement(e.rootElement),e}_append(){if(this._isAppended)return;const e=this._getElement();this._config.rootElement.append(e),EventHandler.on(e,EVENT_MOUSEDOWN,(()=>{execute(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(e){executeAfterTransition(e,this._getElement(),this._config.isAnimated)}}const NAME$8="focustrap",DATA_KEY$5="bs.focustrap",EVENT_KEY$5=`.${DATA_KEY$5}`,EVENT_FOCUSIN$2=`focusin${EVENT_KEY$5}`,EVENT_KEYDOWN_TAB=`keydown.tab${EVENT_KEY$5}`,TAB_KEY="Tab",TAB_NAV_FORWARD="forward",TAB_NAV_BACKWARD="backward",Default$7={autofocus:!0,trapElement:null},DefaultType$7={autofocus:"boolean",trapElement:"element"};class FocusTrap extends Config{constructor(e){super(),this._config=this._getConfig(e),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Default$7}static get DefaultType(){return DefaultType$7}static get NAME(){return NAME$8}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),EventHandler.off(document,EVENT_KEY$5),EventHandler.on(document,EVENT_FOCUSIN$2,(e=>this._handleFocusin(e))),EventHandler.on(document,EVENT_KEYDOWN_TAB,(e=>this._handleKeydown(e))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,EventHandler.off(document,EVENT_KEY$5))}_handleFocusin(e){const{trapElement:t}=this._config;if(e.target===document||e.target===t||t.contains(e.target))return;const s=SelectorEngine.focusableChildren(t);0===s.length?t.focus():"backward"===this._lastTabNavDirection?s[s.length-1].focus():s[0].focus()}_handleKeydown(e){"Tab"===e.key&&(this._lastTabNavDirection=e.shiftKey?"backward":"forward")}}const SELECTOR_FIXED_CONTENT=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",SELECTOR_STICKY_CONTENT=".sticky-top",PROPERTY_PADDING="padding-right",PROPERTY_MARGIN="margin-right";class ScrollBarHelper{constructor(){this._element=document.body}getWidth(){const e=document.documentElement.clientWidth;return Math.abs(window.innerWidth-e)}hide(){const e=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,"padding-right",(t=>t+e)),this._setElementAttributes(SELECTOR_FIXED_CONTENT,"padding-right",(t=>t+e)),this._setElementAttributes(".sticky-top","margin-right",(t=>t-e))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,"padding-right"),this._resetElementAttributes(SELECTOR_FIXED_CONTENT,"padding-right"),this._resetElementAttributes(".sticky-top","margin-right")}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(e,t,s){const i=this.getWidth();this._applyManipulationCallback(e,(e=>{if(e!==this._element&&window.innerWidth>e.clientWidth+i)return;this._saveInitialAttribute(e,t);const n=window.getComputedStyle(e).getPropertyValue(t);e.style.setProperty(t,`${s(Number.parseFloat(n))}px`)}))}_saveInitialAttribute(e,t){const s=e.style.getPropertyValue(t);s&&Manipulator.setDataAttribute(e,t,s)}_resetElementAttributes(e,t){this._applyManipulationCallback(e,(e=>{const s=Manipulator.getDataAttribute(e,t);null!==s?(Manipulator.removeDataAttribute(e,t),e.style.setProperty(t,s)):e.style.removeProperty(t)}))}_applyManipulationCallback(e,t){if(isElement(e))t(e);else for(const s of SelectorEngine.find(e,this._element))t(s)}}const NAME$7="modal",DATA_KEY$4="bs.modal",EVENT_KEY$4=".bs.modal",DATA_API_KEY$2=".data-api",ESCAPE_KEY$1="Escape",EVENT_HIDE$4="hide.bs.modal",EVENT_HIDE_PREVENTED$1="hidePrevented.bs.modal",EVENT_HIDDEN$4="hidden.bs.modal",EVENT_SHOW$4="show.bs.modal",EVENT_SHOWN$4="shown.bs.modal",EVENT_RESIZE$1="resize.bs.modal",EVENT_CLICK_DISMISS="click.dismiss.bs.modal",EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss.bs.modal",EVENT_KEYDOWN_DISMISS$1="keydown.dismiss.bs.modal",EVENT_CLICK_DATA_API$2="click.bs.modal.data-api",CLASS_NAME_OPEN="modal-open",CLASS_NAME_FADE$3="fade",CLASS_NAME_SHOW$4="show",CLASS_NAME_STATIC="modal-static",OPEN_SELECTOR$1=".modal.show",SELECTOR_DIALOG=".modal-dialog",SELECTOR_MODAL_BODY=".modal-body",SELECTOR_DATA_TOGGLE$2='[data-bs-toggle="modal"]',Default$6={backdrop:!0,focus:!0,keyboard:!0},DefaultType$6={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Modal extends BaseComponent{constructor(e,t){super(e,t),this._dialog=SelectorEngine.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new ScrollBarHelper,this._addEventListeners()}static get Default(){return Default$6}static get DefaultType(){return DefaultType$6}static get NAME(){return NAME$7}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown||this._isTransitioning)return;EventHandler.trigger(this._element,EVENT_SHOW$4,{relatedTarget:e}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add("modal-open"),this._adjustDialog(),this._backdrop.show((()=>this._showElement(e))))}hide(){if(!this._isShown||this._isTransitioning)return;EventHandler.trigger(this._element,EVENT_HIDE$4).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove("show"),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated()))}dispose(){EventHandler.off(window,".bs.modal"),EventHandler.off(this._dialog,".bs.modal"),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new Backdrop({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new FocusTrap({trapElement:this._element})}_showElement(e){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const t=SelectorEngine.findOne(".modal-body",this._dialog);t&&(t.scrollTop=0),reflow(this._element),this._element.classList.add("show");this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,EventHandler.trigger(this._element,EVENT_SHOWN$4,{relatedTarget:e})}),this._dialog,this._isAnimated())}_addEventListeners(){EventHandler.on(this._element,EVENT_KEYDOWN_DISMISS$1,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),EventHandler.on(window,EVENT_RESIZE$1,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),EventHandler.on(this._element,EVENT_MOUSEDOWN_DISMISS,(e=>{EventHandler.one(this._element,EVENT_CLICK_DISMISS,(t=>{this._element===e.target&&this._element===t.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove("modal-open"),this._resetAdjustments(),this._scrollBar.reset(),EventHandler.trigger(this._element,EVENT_HIDDEN$4)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED$1).defaultPrevented)return;const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._element.style.overflowY;"hidden"===t||this._element.classList.contains("modal-static")||(e||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),this._queueCallback((()=>{this._element.classList.remove("modal-static"),this._queueCallback((()=>{this._element.style.overflowY=t}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const e=this._element.scrollHeight>document.documentElement.clientHeight,t=this._scrollBar.getWidth(),s=t>0;if(s&&!e){const e=isRTL()?"paddingLeft":"paddingRight";this._element.style[e]=`${t}px`}if(!s&&e){const e=isRTL()?"paddingRight":"paddingLeft";this._element.style[e]=`${t}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(e,t){return this.each((function(){const s=Modal.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===s[e])throw new TypeError(`No method named "${e}"`);s[e](t)}}))}}EventHandler.on(document,EVENT_CLICK_DATA_API$2,SELECTOR_DATA_TOGGLE$2,(function(e){const t=SelectorEngine.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&e.preventDefault(),EventHandler.one(t,EVENT_SHOW$4,(e=>{e.defaultPrevented||EventHandler.one(t,EVENT_HIDDEN$4,(()=>{isVisible(this)&&this.focus()}))}));const s=SelectorEngine.findOne(".modal.show");s&&Modal.getInstance(s).hide();Modal.getOrCreateInstance(t).toggle(this)})),enableDismissTrigger(Modal),defineJQueryPlugin(Modal);const NAME$6="offcanvas",DATA_KEY$3="bs.offcanvas",EVENT_KEY$3=`.${DATA_KEY$3}`,DATA_API_KEY$1=".data-api",EVENT_LOAD_DATA_API$2=`load${EVENT_KEY$3}.data-api`,ESCAPE_KEY="Escape",CLASS_NAME_SHOW$3="show",CLASS_NAME_SHOWING$1="showing",CLASS_NAME_HIDING="hiding",CLASS_NAME_BACKDROP="offcanvas-backdrop",OPEN_SELECTOR=".offcanvas.show",EVENT_SHOW$3=`show${EVENT_KEY$3}`,EVENT_SHOWN$3=`shown${EVENT_KEY$3}`,EVENT_HIDE$3=`hide${EVENT_KEY$3}`,EVENT_HIDE_PREVENTED=`hidePrevented${EVENT_KEY$3}`,EVENT_HIDDEN$3=`hidden${EVENT_KEY$3}`,EVENT_RESIZE=`resize${EVENT_KEY$3}`,EVENT_CLICK_DATA_API$1=`click${EVENT_KEY$3}.data-api`,EVENT_KEYDOWN_DISMISS=`keydown.dismiss${EVENT_KEY$3}`,SELECTOR_DATA_TOGGLE$1='[data-bs-toggle="offcanvas"]',Default$5={backdrop:!0,keyboard:!0,scroll:!1},DefaultType$5={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Offcanvas extends BaseComponent{constructor(e,t){super(e,t),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Default$5}static get DefaultType(){return DefaultType$5}static get NAME(){return NAME$6}toggle(e){return this._isShown?this.hide():this.show(e)}show(e){if(this._isShown)return;if(EventHandler.trigger(this._element,EVENT_SHOW$3,{relatedTarget:e}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new ScrollBarHelper).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add("showing");this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add("show"),this._element.classList.remove("showing"),EventHandler.trigger(this._element,EVENT_SHOWN$3,{relatedTarget:e})}),this._element,!0)}hide(){if(!this._isShown)return;if(EventHandler.trigger(this._element,EVENT_HIDE$3).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add("hiding"),this._backdrop.hide();this._queueCallback((()=>{this._element.classList.remove("show","hiding"),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new ScrollBarHelper).reset(),EventHandler.trigger(this._element,EVENT_HIDDEN$3)}),this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const e=Boolean(this._config.backdrop);return new Backdrop({className:CLASS_NAME_BACKDROP,isVisible:e,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:e?()=>{"static"!==this._config.backdrop?this.hide():EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED)}:null})}_initializeFocusTrap(){return new FocusTrap({trapElement:this._element})}_addEventListeners(){EventHandler.on(this._element,EVENT_KEYDOWN_DISMISS,(e=>{"Escape"===e.key&&(this._config.keyboard?this.hide():EventHandler.trigger(this._element,EVENT_HIDE_PREVENTED))}))}static jQueryInterface(e){return this.each((function(){const t=Offcanvas.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}EventHandler.on(document,EVENT_CLICK_DATA_API$1,SELECTOR_DATA_TOGGLE$1,(function(e){const t=SelectorEngine.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&e.preventDefault(),isDisabled(this))return;EventHandler.one(t,EVENT_HIDDEN$3,(()=>{isVisible(this)&&this.focus()}));const s=SelectorEngine.findOne(OPEN_SELECTOR);s&&s!==t&&Offcanvas.getInstance(s).hide();Offcanvas.getOrCreateInstance(t).toggle(this)})),EventHandler.on(window,EVENT_LOAD_DATA_API$2,(()=>{for(const e of SelectorEngine.find(OPEN_SELECTOR))Offcanvas.getOrCreateInstance(e).show()})),EventHandler.on(window,EVENT_RESIZE,(()=>{for(const e of SelectorEngine.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(e).position&&Offcanvas.getOrCreateInstance(e).hide()})),enableDismissTrigger(Offcanvas),defineJQueryPlugin(Offcanvas);const uriAttributes=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),SAFE_URL_PATTERN=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i,DATA_URL_PATTERN=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i,allowedAttribute=(e,t)=>{const s=e.nodeName.toLowerCase();return t.includes(s)?!uriAttributes.has(s)||Boolean(SAFE_URL_PATTERN.test(e.nodeValue)||DATA_URL_PATTERN.test(e.nodeValue)):t.filter((e=>e instanceof RegExp)).some((e=>e.test(s)))},ARIA_ATTRIBUTE_PATTERN=/^aria-[\w-]*$/i,DefaultAllowlist={"*":["class","dir","id","lang","role",ARIA_ATTRIBUTE_PATTERN],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};function sanitizeHtml(e,t,s){if(!e.length)return e;if(s&&"function"==typeof s)return s(e);const i=(new window.DOMParser).parseFromString(e,"text/html"),n=[].concat(...i.body.querySelectorAll("*"));for(const e of n){const s=e.nodeName.toLowerCase();if(!Object.keys(t).includes(s)){e.remove();continue}const i=[].concat(...e.attributes),n=[].concat(t["*"]||[],t[s]||[]);for(const t of i)allowedAttribute(t,n)||e.removeAttribute(t.nodeName)}return i.body.innerHTML}const NAME$5="TemplateFactory",Default$4={allowList:DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},DefaultType$4={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},DefaultContentType={entry:"(string|element|function|null)",selector:"(string|element)"};class TemplateFactory extends Config{constructor(e){super(),this._config=this._getConfig(e)}static get Default(){return Default$4}static get DefaultType(){return DefaultType$4}static get NAME(){return NAME$5}getContent(){return Object.values(this._config.content).map((e=>this._resolvePossibleFunction(e))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(e){return this._checkContent(e),this._config.content={...this._config.content,...e},this}toHtml(){const e=document.createElement("div");e.innerHTML=this._maybeSanitize(this._config.template);for(const[t,s]of Object.entries(this._config.content))this._setContent(e,s,t);const t=e.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&t.classList.add(...s.split(" ")),t}_typeCheckConfig(e){super._typeCheckConfig(e),this._checkContent(e.content)}_checkContent(e){for(const[t,s]of Object.entries(e))super._typeCheckConfig({selector:t,entry:s},DefaultContentType)}_setContent(e,t,s){const i=SelectorEngine.findOne(s,e);i&&((t=this._resolvePossibleFunction(t))?isElement(t)?this._putElementInTemplate(getElement(t),i):this._config.html?i.innerHTML=this._maybeSanitize(t):i.textContent=t:i.remove())}_maybeSanitize(e){return this._config.sanitize?sanitizeHtml(e,this._config.allowList,this._config.sanitizeFn):e}_resolvePossibleFunction(e){return execute(e,[this])}_putElementInTemplate(e,t){if(this._config.html)return t.innerHTML="",void t.append(e);t.textContent=e.textContent}}const NAME$4="tooltip",DISALLOWED_ATTRIBUTES=new Set(["sanitize","allowList","sanitizeFn"]),CLASS_NAME_FADE$2="fade",CLASS_NAME_MODAL="modal",CLASS_NAME_SHOW$2="show",SELECTOR_TOOLTIP_INNER=".tooltip-inner",SELECTOR_MODAL=".modal",EVENT_MODAL_HIDE="hide.bs.modal",TRIGGER_HOVER="hover",TRIGGER_FOCUS="focus",TRIGGER_CLICK="click",TRIGGER_MANUAL="manual",EVENT_HIDE$2="hide",EVENT_HIDDEN$2="hidden",EVENT_SHOW$2="show",EVENT_SHOWN$2="shown",EVENT_INSERTED="inserted",EVENT_CLICK$1="click",EVENT_FOCUSIN$1="focusin",EVENT_FOCUSOUT$1="focusout",EVENT_MOUSEENTER="mouseenter",EVENT_MOUSELEAVE="mouseleave",AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:isRTL()?"left":"right",BOTTOM:"bottom",LEFT:isRTL()?"right":"left"},Default$3={allowList:DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},DefaultType$3={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Tooltip extends BaseComponent{constructor(e,t){if(void 0===Popper)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,t),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Default$3}static get DefaultType(){return DefaultType$3}static get NAME(){return NAME$4}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),EventHandler.off(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const e=EventHandler.trigger(this._element,this.constructor.eventName("show")),t=(findShadowRoot(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(e.defaultPrevented||!t)return;this._disposePopper();const s=this._getTipElement();this._element.setAttribute("aria-describedby",s.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(s),EventHandler.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(s),s.classList.add("show"),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))EventHandler.on(e,"mouseover",noop);this._queueCallback((()=>{EventHandler.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(EventHandler.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove("show"),"ontouchstart"in document.documentElement)for(const e of[].concat(...document.body.children))EventHandler.off(e,"mouseover",noop);this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,this._isHovered=null;this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),EventHandler.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(e){const t=this._getTemplateFactory(e).toHtml();if(!t)return null;t.classList.remove("fade","show"),t.classList.add(`bs-${this.constructor.NAME}-auto`);const s=getUID(this.constructor.NAME).toString();return t.setAttribute("id",s),this._isAnimated()&&t.classList.add("fade"),t}setContent(e){this._newContent=e,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(e){return this._templateFactory?this._templateFactory.changeContent(e):this._templateFactory=new TemplateFactory({...this._config,content:e,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(e){return this.constructor.getOrCreateInstance(e.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains("fade")}_isShown(){return this.tip&&this.tip.classList.contains("show")}_createPopper(e){const t=execute(this._config.placement,[this,e,this._element]),s=AttachmentMap[t.toUpperCase()];return createPopper(this._element,e,this._getPopperConfig(s))}_getOffset(){const{offset:e}=this._config;return"string"==typeof e?e.split(",").map((e=>Number.parseInt(e,10))):"function"==typeof e?t=>e(t,this._element):e}_resolvePossibleFunction(e){return execute(e,[this._element])}_getPopperConfig(e){const t={placement:e,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:e=>{this._getTipElement().setAttribute("data-popper-placement",e.state.placement)}}]};return{...t,...execute(this._config.popperConfig,[t])}}_setListeners(){const e=this._config.trigger.split(" ");for(const t of e)if("click"===t)EventHandler.on(this._element,this.constructor.eventName("click"),this._config.selector,(e=>{this._initializeOnDelegatedTarget(e).toggle()}));else if("manual"!==t){const e="hover"===t?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),s="hover"===t?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");EventHandler.on(this._element,e,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusin"===e.type?"focus":"hover"]=!0,t._enter()})),EventHandler.on(this._element,s,this._config.selector,(e=>{const t=this._initializeOnDelegatedTarget(e);t._activeTrigger["focusout"===e.type?"focus":"hover"]=t._element.contains(e.relatedTarget),t._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},EventHandler.on(this._element.closest(".modal"),"hide.bs.modal",this._hideModalHandler)}_fixTitle(){const e=this._element.getAttribute("title");e&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",e),this._element.setAttribute("data-bs-original-title",e),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(e,t){clearTimeout(this._timeout),this._timeout=setTimeout(e,t)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(e){const t=Manipulator.getDataAttributes(this._element);for(const e of Object.keys(t))DISALLOWED_ATTRIBUTES.has(e)&&delete t[e];return e={...t,..."object"==typeof e&&e?e:{}},e=this._mergeConfigObj(e),e=this._configAfterMerge(e),this._typeCheckConfig(e),e}_configAfterMerge(e){return e.container=!1===e.container?document.body:getElement(e.container),"number"==typeof e.delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),e}_getDelegateConfig(){const e={};for(const[t,s]of Object.entries(this._config))this.constructor.Default[t]!==s&&(e[t]=s);return e.selector=!1,e.trigger="manual",e}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(e){return this.each((function(){const t=Tooltip.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}defineJQueryPlugin(Tooltip);const NAME$3="popover",SELECTOR_TITLE=".popover-header",SELECTOR_CONTENT=".popover-body",Default$2={...Tooltip.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},DefaultType$2={...Tooltip.DefaultType,content:"(null|string|element|function)"};class Popover extends Tooltip{static get Default(){return Default$2}static get DefaultType(){return DefaultType$2}static get NAME(){return NAME$3}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[SELECTOR_TITLE]:this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(e){return this.each((function(){const t=Popover.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e]()}}))}}defineJQueryPlugin(Popover);const NAME$2="scrollspy",DATA_KEY$2="bs.scrollspy",EVENT_KEY$2=`.${DATA_KEY$2}`,DATA_API_KEY=".data-api",EVENT_ACTIVATE=`activate${EVENT_KEY$2}`,EVENT_CLICK=`click${EVENT_KEY$2}`,EVENT_LOAD_DATA_API$1=`load${EVENT_KEY$2}.data-api`,CLASS_NAME_DROPDOWN_ITEM="dropdown-item",CLASS_NAME_ACTIVE$1="active",SELECTOR_DATA_SPY='[data-bs-spy="scroll"]',SELECTOR_TARGET_LINKS="[href]",SELECTOR_NAV_LIST_GROUP=".nav, .list-group",SELECTOR_NAV_LINKS=".nav-link",SELECTOR_NAV_ITEMS=".nav-item",SELECTOR_LIST_ITEMS=".list-group-item",SELECTOR_LINK_ITEMS=".nav-link, .nav-item > .nav-link, .list-group-item",SELECTOR_DROPDOWN=".dropdown",SELECTOR_DROPDOWN_TOGGLE$1=".dropdown-toggle",Default$1={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},DefaultType$1={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class ScrollSpy extends BaseComponent{constructor(e,t){super(e,t),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Default$1}static get DefaultType(){return DefaultType$1}static get NAME(){return NAME$2}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const e of this._observableSections.values())this._observer.observe(e)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(e){return e.target=getElement(e.target)||document.body,e.rootMargin=e.offset?`${e.offset}px 0px -30%`:e.rootMargin,"string"==typeof e.threshold&&(e.threshold=e.threshold.split(",").map((e=>Number.parseFloat(e)))),e}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(EventHandler.off(this._config.target,EVENT_CLICK),EventHandler.on(this._config.target,EVENT_CLICK,"[href]",(e=>{const t=this._observableSections.get(e.target.hash);if(t){e.preventDefault();const s=this._rootElement||window,i=t.offsetTop-this._element.offsetTop;if(s.scrollTo)return void s.scrollTo({top:i,behavior:"smooth"});s.scrollTop=i}})))}_getNewObserver(){const e={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((e=>this._observerCallback(e)),e)}_observerCallback(e){const t=e=>this._targetLinks.get(`#${e.target.id}`),s=e=>{this._previousScrollData.visibleEntryTop=e.target.offsetTop,this._process(t(e))},i=(this._rootElement||document.documentElement).scrollTop,n=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const r of e){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(t(r));continue}const e=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&e){if(s(r),!i)return}else n||e||s(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const e=SelectorEngine.find("[href]",this._config.target);for(const t of e){if(!t.hash||isDisabled(t))continue;const e=SelectorEngine.findOne(t.hash,this._element);isVisible(e)&&(this._targetLinks.set(t.hash,t),this._observableSections.set(t.hash,e))}}_process(e){this._activeTarget!==e&&(this._clearActiveClass(this._config.target),this._activeTarget=e,e.classList.add("active"),this._activateParents(e),EventHandler.trigger(this._element,EVENT_ACTIVATE,{relatedTarget:e}))}_activateParents(e){if(e.classList.contains("dropdown-item"))SelectorEngine.findOne(".dropdown-toggle",e.closest(".dropdown")).classList.add("active");else for(const t of SelectorEngine.parents(e,".nav, .list-group"))for(const e of SelectorEngine.prev(t,SELECTOR_LINK_ITEMS))e.classList.add("active")}_clearActiveClass(e){e.classList.remove("active");const t=SelectorEngine.find("[href].active",e);for(const e of t)e.classList.remove("active")}static jQueryInterface(e){return this.each((function(){const t=ScrollSpy.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}EventHandler.on(window,EVENT_LOAD_DATA_API$1,(()=>{for(const e of SelectorEngine.find(SELECTOR_DATA_SPY))ScrollSpy.getOrCreateInstance(e)})),defineJQueryPlugin(ScrollSpy);const NAME$1="tab",DATA_KEY$1="bs.tab",EVENT_KEY$1=".bs.tab",EVENT_HIDE$1="hide.bs.tab",EVENT_HIDDEN$1="hidden.bs.tab",EVENT_SHOW$1="show.bs.tab",EVENT_SHOWN$1="shown.bs.tab",EVENT_CLICK_DATA_API="click.bs.tab",EVENT_KEYDOWN="keydown.bs.tab",EVENT_LOAD_DATA_API="load.bs.tab",ARROW_LEFT_KEY="ArrowLeft",ARROW_RIGHT_KEY="ArrowRight",ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",CLASS_NAME_ACTIVE="active",CLASS_NAME_FADE$1="fade",CLASS_NAME_SHOW$1="show",CLASS_DROPDOWN="dropdown",SELECTOR_DROPDOWN_TOGGLE=".dropdown-toggle",SELECTOR_DROPDOWN_MENU=".dropdown-menu",NOT_SELECTOR_DROPDOWN_TOGGLE=":not(.dropdown-toggle)",SELECTOR_TAB_PANEL='.list-group, .nav, [role="tablist"]',SELECTOR_OUTER=".nav-item, .list-group-item",SELECTOR_INNER='.nav-link:not(.dropdown-toggle), .list-group-item:not(.dropdown-toggle), [role="tab"]:not(.dropdown-toggle)',SELECTOR_DATA_TOGGLE='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',SELECTOR_INNER_ELEM=`${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`,SELECTOR_DATA_TOGGLE_ACTIVE='.active[data-bs-toggle="tab"], .active[data-bs-toggle="pill"], .active[data-bs-toggle="list"]';class Tab extends BaseComponent{constructor(e){super(e),this._parent=this._element.closest(SELECTOR_TAB_PANEL),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),EventHandler.on(this._element,EVENT_KEYDOWN,(e=>this._keydown(e))))}static get NAME(){return"tab"}show(){const e=this._element;if(this._elemIsActive(e))return;const t=this._getActiveElem(),s=t?EventHandler.trigger(t,EVENT_HIDE$1,{relatedTarget:e}):null;EventHandler.trigger(e,EVENT_SHOW$1,{relatedTarget:t}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(t,e),this._activate(e,t))}_activate(e,t){if(!e)return;e.classList.add("active"),this._activate(SelectorEngine.getElementFromSelector(e));this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.removeAttribute("tabindex"),e.setAttribute("aria-selected",!0),this._toggleDropDown(e,!0),EventHandler.trigger(e,EVENT_SHOWN$1,{relatedTarget:t})):e.classList.add("show")}),e,e.classList.contains("fade"))}_deactivate(e,t){if(!e)return;e.classList.remove("active"),e.blur(),this._deactivate(SelectorEngine.getElementFromSelector(e));this._queueCallback((()=>{"tab"===e.getAttribute("role")?(e.setAttribute("aria-selected",!1),e.setAttribute("tabindex","-1"),this._toggleDropDown(e,!1),EventHandler.trigger(e,EVENT_HIDDEN$1,{relatedTarget:t})):e.classList.remove("show")}),e,e.classList.contains("fade"))}_keydown(e){if(![ARROW_LEFT_KEY,ARROW_RIGHT_KEY,ARROW_UP_KEY,ARROW_DOWN_KEY].includes(e.key))return;e.stopPropagation(),e.preventDefault();const t=[ARROW_RIGHT_KEY,ARROW_DOWN_KEY].includes(e.key),s=getNextActiveElement(this._getChildren().filter((e=>!isDisabled(e))),e.target,t,!0);s&&(s.focus({preventScroll:!0}),Tab.getOrCreateInstance(s).show())}_getChildren(){return SelectorEngine.find(SELECTOR_INNER_ELEM,this._parent)}_getActiveElem(){return this._getChildren().find((e=>this._elemIsActive(e)))||null}_setInitialAttributes(e,t){this._setAttributeIfNotExists(e,"role","tablist");for(const e of t)this._setInitialAttributesOnChild(e)}_setInitialAttributesOnChild(e){e=this._getInnerElement(e);const t=this._elemIsActive(e),s=this._getOuterElement(e);e.setAttribute("aria-selected",t),s!==e&&this._setAttributeIfNotExists(s,"role","presentation"),t||e.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(e,"role","tab"),this._setInitialAttributesOnTargetPanel(e)}_setInitialAttributesOnTargetPanel(e){const t=SelectorEngine.getElementFromSelector(e);t&&(this._setAttributeIfNotExists(t,"role","tabpanel"),e.id&&this._setAttributeIfNotExists(t,"aria-labelledby",`${e.id}`))}_toggleDropDown(e,t){const s=this._getOuterElement(e);if(!s.classList.contains("dropdown"))return;const i=(e,i)=>{const n=SelectorEngine.findOne(e,s);n&&n.classList.toggle(i,t)};i(".dropdown-toggle","active"),i(".dropdown-menu","show"),s.setAttribute("aria-expanded",t)}_setAttributeIfNotExists(e,t,s){e.hasAttribute(t)||e.setAttribute(t,s)}_elemIsActive(e){return e.classList.contains("active")}_getInnerElement(e){return e.matches(SELECTOR_INNER_ELEM)?e:SelectorEngine.findOne(SELECTOR_INNER_ELEM,e)}_getOuterElement(e){return e.closest(SELECTOR_OUTER)||e}static jQueryInterface(e){return this.each((function(){const t=Tab.getOrCreateInstance(this);if("string"==typeof e){if(void 0===t[e]||e.startsWith("_")||"constructor"===e)throw new TypeError(`No method named "${e}"`);t[e]()}}))}}EventHandler.on(document,"click.bs.tab",SELECTOR_DATA_TOGGLE,(function(e){["A","AREA"].includes(this.tagName)&&e.preventDefault(),isDisabled(this)||Tab.getOrCreateInstance(this).show()})),EventHandler.on(window,"load.bs.tab",(()=>{for(const e of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE))Tab.getOrCreateInstance(e)})),defineJQueryPlugin(Tab);const NAME="toast",DATA_KEY="bs.toast",EVENT_KEY=`.${DATA_KEY}`,EVENT_MOUSEOVER=`mouseover${EVENT_KEY}`,EVENT_MOUSEOUT=`mouseout${EVENT_KEY}`,EVENT_FOCUSIN=`focusin${EVENT_KEY}`,EVENT_FOCUSOUT=`focusout${EVENT_KEY}`,EVENT_HIDE=`hide${EVENT_KEY}`,EVENT_HIDDEN=`hidden${EVENT_KEY}`,EVENT_SHOW=`show${EVENT_KEY}`,EVENT_SHOWN=`shown${EVENT_KEY}`,CLASS_NAME_FADE="fade",CLASS_NAME_HIDE="hide",CLASS_NAME_SHOW="show",CLASS_NAME_SHOWING="showing",DefaultType={animation:"boolean",autohide:"boolean",delay:"number"},Default={animation:!0,autohide:!0,delay:5e3};class Toast extends BaseComponent{constructor(e,t){super(e,t),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return NAME}show(){if(EventHandler.trigger(this._element,EVENT_SHOW).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove("hide"),reflow(this._element),this._element.classList.add("show","showing"),this._queueCallback((()=>{this._element.classList.remove("showing"),EventHandler.trigger(this._element,EVENT_SHOWN),this._maybeScheduleHide()}),this._element,this._config.animation)}hide(){if(!this.isShown())return;if(EventHandler.trigger(this._element,EVENT_HIDE).defaultPrevented)return;this._element.classList.add("showing"),this._queueCallback((()=>{this._element.classList.add("hide"),this._element.classList.remove("showing","show"),EventHandler.trigger(this._element,EVENT_HIDDEN)}),this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove("show"),super.dispose()}isShown(){return this._element.classList.contains("show")}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(e,t){switch(e.type){case"mouseover":case"mouseout":this._hasMouseInteraction=t;break;case"focusin":case"focusout":this._hasKeyboardInteraction=t}if(t)return void this._clearTimeout();const s=e.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){EventHandler.on(this._element,EVENT_MOUSEOVER,(e=>this._onInteraction(e,!0))),EventHandler.on(this._element,EVENT_MOUSEOUT,(e=>this._onInteraction(e,!1))),EventHandler.on(this._element,EVENT_FOCUSIN,(e=>this._onInteraction(e,!0))),EventHandler.on(this._element,EVENT_FOCUSOUT,(e=>this._onInteraction(e,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(e){return this.each((function(){const t=Toast.getOrCreateInstance(this,e);if("string"==typeof e){if(void 0===t[e])throw new TypeError(`No method named "${e}"`);t[e](this)}}))}}enableDismissTrigger(Toast),defineJQueryPlugin(Toast);var dropdownTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]'));dropdownTriggerList.map((function(e){var t={boundary:"viewport"===e.getAttribute("data-bs-boundary")?document.querySelector(".btn"):"clippingParents"};return new Dropdown(e,t)}));var tooltipTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));tooltipTriggerList.map((function(e){var t,s,i={delay:{show:50,hide:50},html:null!==(t="true"===e.getAttribute("data-bs-html"))&&void 0!==t&&t,placement:null!==(s=e.getAttribute("data-bs-placement"))&&void 0!==s?s:"auto"};return new Tooltip(e,i)}));var popoverTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));popoverTriggerList.map((function(e){var t,s,i={delay:{show:50,hide:50},html:null!==(t="true"===e.getAttribute("data-bs-html"))&&void 0!==t&&t,placement:null!==(s=e.getAttribute("data-bs-placement"))&&void 0!==s?s:"auto"};return new Popover(e,i)}));var switchesTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]'));switchesTriggerList.map((function(e){e.addEventListener("click",(function(t){t.stopPropagation(),e.classList.toggle("active")}))}));var toastsTriggerList=[].slice.call(document.querySelectorAll('[data-bs-toggle="toast"]'));toastsTriggerList.map((function(e){return new Toast(e)}));