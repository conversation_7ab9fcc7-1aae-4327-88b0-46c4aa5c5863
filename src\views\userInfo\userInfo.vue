<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.fullname" :placeholder="$t('userInfo.label2')" style="width: 100px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.tel" :placeholder="$t('userInfo.label3')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.userName" :placeholder="$t('userInfo.label1')" style="width: 200px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.parent_ID" :placeholder="$t('userInfo.label13')" style="width: 150px;" class="filter-item" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.backup5" :placeholder="$t('userInfo.label14')" style="width: 180px" clearable class="filter-item" @keyup.enter.native="handleFilter">
        <el-option v-for="item in tagOptions" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
      <el-button v-waves class="filter-item" type="primary" icon="el-icon-search" @click="handleFilter">{{ $t('userTable.search') }}</el-button>
      <el-button v-if="(this.dqryType=='999'||this.dqryType=='0')?true:false" class="filter-item" style="margin-left: 10px;" type="primary" icon="el-icon-plus" @click="handleCreate">{{ $t('userInfo.addCrmUser') }}</el-button>
      <el-button v-waves class="filter-item" type="primary" @click="handleExport">{{ $t('common.exportReport') }}</el-button>
    </div>
    <el-table :key="tableKey" v-loading="listLoading" :data="list" tooltip-effect="dark" border fit highlight-current-row style="width: 100%;" @sort-change="sortChange">
      <el-table-column :label="$t('userTable.number')" width="50px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.sortNum }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label2')" prop="fullname" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}{{ scope.row.surname }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label1')" prop="userName" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userName }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label3')" prop="tel" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.tel }}</span>
        </template>
      </el-table-column>

      <el-table-column :label="$t('userInfo.label17')" min-width="180px" align="center" prop="gmtCreate" sortable="custom">
        <template slot-scope="scope">
          <span>{{ scope.row.gmtCreate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label13')" prop="backup2" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.backup2 }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userInfo.label14')" prop="kycInfo" min-width="120px" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.kycInfo }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('userTable.status')" class-name="status-col" width="100">
        <template slot-scope="scope">
          <el-switch v-model="scope.row.isAgent" active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0" @change="updateIsAvailable(scope.row)" />
        </template>
      </el-table-column>
      <el-table-column v-if="(this.dqryType=='999'||this.dqryType=='0')?true:false" :label="$t('userTable.actions')" align="center" width="420" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="handleUpdate(scope.row)">{{ $t('userInfo.label15') }}</el-button>
          <el-button type="primary" size="mini" @click="handleEdit(scope.row)">{{ $t('userInfo.label16') }}</el-button>
          <el-button type="warning" size="small" @click="handleResetPassword(scope.row)">
            {{ $t('userTable.resetPassword') }}
          </el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">{{ $t('userTable.delete') }}</el-button>
        </template>
      </el-table-column>
      <el-table-column v-if="(this.dqryType!='999'&&this.dqryType!='0')?true:false" :label="$t('userTable.actions')" align="center" width="120" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" @click="handleUpdate(scope.row)">{{ $t('userInfo.label15') }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.page" :limit.sync="listQuery.limit" @pagination="getList" />

    <el-dialog :title="$t('userInfo.addCrmUser')" :visible.sync="dialogFormAddVisible" :close-on-click-modal="false">
      <el-form ref="dataForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">

        <el-form-item :label="$t('userInfo.label1')" prop="userName">
          <el-input v-model="temp.userName" />
        </el-form-item>
        <el-form-item :label="$t('navbar.initialPassword')" prop="password">
          <el-input v-model="temp.password" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label2')" prop="fullname">
          <el-input v-model="temp.name" style="width:150px;" :placeholder="$t('userInfo.inputFirstName')" /> &nbsp;&nbsp;<el-input v-model="temp.surname" style="width:150px;" :placeholder="$t('userInfo.inputLastName')" />
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          <el-input v-model="temp.birthday" />
        </el-form-item>

        <el-form-item :label="$t('userInfo.label3')" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label4')" prop="province">
          <el-input v-model="temp.province" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label5')" prop="country">
          <el-input v-model="temp.country" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankName')" prop="city">
          <el-input v-model="temp.city" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankAccount')" prop="adress">
          <el-input v-model="temp.adress" />
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          <el-input v-model="temp.identityNum" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label14')">
          <el-select v-model="rekebackRule2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible = false">{{ $t('userInfoAudit.label19') }}</el-button>
        <el-button type="primary" @click="createData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="$t('userInfo.updateInfo')" :visible.sync="dialogFormAddVisible2" :close-on-click-modal="false">
      <el-form ref="dataForm2" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">

        <el-form-item :label="$t('userInfo.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userInfo.label2')" prop="fullname">
          <el-input v-model="temp.name" style="width:150px;" /> &nbsp;&nbsp;<el-input v-model="temp.surname" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          <el-input v-model="temp.birthday" />
        </el-form-item>

        <el-form-item :label="$t('userInfo.label3')" prop="tel">
          <el-input v-model="temp.tel" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label4')" prop="province">
          <el-input v-model="temp.province" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label5')" prop="country">
          <el-input v-model="temp.country" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankName')" prop="city">
          <el-input v-model="temp.city" style="width:150px;" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankAccount')" prop="adress">
          <el-input v-model="temp.adress" />
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          <el-input v-model="temp.identityNum" />
        </el-form-item>
        <el-form-item :label="$t('userInfo.label14')">
          <el-select v-model="rekebackRule2" multiple :placeholder="$t('userTable.placeholder3')" style="width:500px;">
            <el-option
              v-for="item in option1s"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="PIC:" prop="imageFront">
          <el-image v-if="temp.imageFront" :src="temp.imageFront" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.imageBack" :src="temp.imageBack" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.backup3" :src="temp.backup3" style="height:150px;" :preview-src-list="srcList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormAddVisible2 = false">{{ $t('userInfoAudit.label19') }}</el-button>
        <el-button type="primary" @click="updateData()"> {{ $t('table.confirm') }}</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="$t('userInfo.viewInfo')" :visible.sync="dialogFormEditVisible" :close-on-click-modal="false">
      <el-form ref="dataEditForm" :rules="rules" :model="temp" label-position="right" label-width="100px" style="max-width: 600px; margin-left:10px;">
        <el-form-item :label="$t('userInfoAudit.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label2')" prop="fullname">
          {{ temp.name }}	{{ temp.surname }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label8')" prop="birthday">
          {{ temp.remark }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label3')" prop="tel">
          {{ temp.tel }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label4')" prop="tel">
          {{ temp.province }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label5')" prop="country">
          {{ temp.country }}
        </el-form-item>
        <el-form-item :label="$t('userInfoAudit.label6')" prop="identityNum">
          {{ temp.identityNum }}
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankName')" prop="city">
          {{ temp.city }}
        </el-form-item>
        <el-form-item :label="$t('userInfo.bankAccount')" prop="address">
          {{ temp.adress }}
        </el-form-item>

        <el-form-item :label="$t('userInfoAudit.label7')" prop="imageFront">
          <el-image v-if="temp.imageFront" :src="temp.imageFront" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.imageBack" :src="temp.imageBack" style="height:150px;" :preview-src-list="srcList" />
          <el-image v-if="temp.backup3" :src="temp.backup3" style="height:150px;" :preview-src-list="srcList" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormEditVisible = false">{{ $t('table.close') }}</el-button>
      </div>
    </el-dialog>

    <!--重置密码弹窗-->
    <el-dialog :title="$t('userTable.resetPassword')" :visible.sync="dialogFormResetPasswordVisible" :close-on-click-modal="false">
      <el-form ref="dataPassForm" :rules="rules" :model="temp" label-position="right" label-width="80px" style="max-width: 400px; margin-left:10px;">
        <el-form-item :label="$t('userInfo.label1')" prop="userName">
          {{ temp.userName }}
        </el-form-item>
        <el-form-item :label="$t('userTable.newPassword')" prop="password">
          <el-input v-model="temp.password" :placeholder="$t('userTable.newPassword')" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormResetPasswordVisible = false">
          {{ $t('table.cancel') }}
        </el-button>
        <el-button type="primary" @click="resetPassword()">
          {{ $t('table.confirm') }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  	getUploadUrl
} from '@/api/upload'
import waves from '@/directive/waves' // waves directive
import { fetchList, fetchUserInfo, createUserInfo, updateUserInfo, updateIsAvailable, removeUserInfo, resetPassword, exportUserInfoExcel } from '@/api/userInfo'
import { fetchALLList } from '@/api/userTag'
import { parseTime } from '@/utils'
import { getToken } from '@/utils/auth.js'
import Pagination from '@/components/Pagination'
import { getInfo } from '@/api/navbar'
import Setting from '@/settings'
export default {
  name: 'UserInfoTable',
  components: { Pagination },
  directives: { waves },
  data() {
    return {
      uploadUrl: '',
      tableKey: 0,
      list: null,
      total: 0,
			    listLoading: true,
      listQuery: {
			 		page: 1,
        limit: 10,
        fullname: undefined,
        userName: undefined,
        email: undefined,
        parent_ID: undefined,
        tel: undefined,
        gmtCreateSearchBegin: undefined,
        gmtCreateSearchEnd: undefined,
        invitationCode: undefined,
        userType: undefined,
        parentId: undefined,
        pinyin: undefined,
        isAgent: undefined,
        backup5: undefined,
        isAvailable: undefined

      },	listQuery2: {
			 		page: 1,
        limit: 1000

      },
      tagOptions: [],
      option1s: [],
      rekebackRule2: [],
      pickerOptions: {
        shortcuts: [{
          text: 'Last Week',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }

        }, {
          text: 'Last Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: 'Last Three Month',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
					 }]				},
      uploadData: {
        id: undefined,
        type: 1
      },
      myHeaders: {
				 'X-Token': getToken()
      },
      temp: {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
          	password: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        imageBack: '',
        invitationCode: '',
        rekebackRule: [],
        roles: [],
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: '',

        contactInformations: [{
          value: ''
        }]

      }, dqryType: undefined, xbList: [

          					{
          						value: '1',
          						label: '男'
          					},
          					{
          						value: '2',
          						label: '女'
          					}
          					],

      srcList: [

      ],
      isavas: [
                            					{
                            						value: 1,
                            						label: 'verified'
                            					},
                            					{
                            						value: 0,
                            						label: 'unverified'
                            					}
                            					],
      valueGmtCreate: undefined,
      dialogFormResetPasswordVisible: false,
      dialogFormAddVisible: false,
      dialogFormAddVisible2: false,
      dialogFormEditVisible: false,
      rules: {
        surname: [
          { required: true, message: this.$t('userInfo.validation.surnameRequired'), trigger: 'change' },,
        ],
        name: [
          { required: true, message: this.$t('userInfo.validation.nameRequired'), trigger: 'change' },,
        ],
        fullname: [
        ],
        userName: [
          { required: true, message: this.$t('userInfo.validation.userNameRequired'), trigger: 'change' },,
        ],
        email: [,
        ],
        tel: [,
        ],
        password: [
          { required: true, message: this.$t('userInfo.validation.passwordRequired'), trigger: 'change' }
        ],
        country: [
        { required: true, message: this.$t('userInfo.validation.countryRequired'), trigger: 'change' }
        ],
        province: [
        { required: true, message: this.$t('userInfo.validation.provinceRequired'), trigger: 'change' }
        ],
        city: [
        ],
        gender: [
        ],
        adress: [
        ],
        nickname: [
        ],
        birthday: [
        ],
        identityNum: [
        ],
        imageFront: [
        ],
        imageBack: [
        ],
        invitationCode: [
        ],
        amountMain: [
        ],
        amountSub: [
        ],
        userType: [
        ],
        parentId: [
        ],
        pinyin: [
        ],
        remark: [
        ],
        rakeBackType: [
        ],
        rakeBackAmount: [
        ],
        isAgent: [
        ],
        auditId: [
        ],
        auditTime: [
        ],
        backup1: [
        ],
        backup2: [
        ],
        backup3: [
        ],
        backup4: [
        ],
        backup5: [
        ],
        backup6: [
        ]
      }
    }
  },
  created() {
    getInfo().then(response => {
        	if (response.code == 20000) {
        this.dqryType = response.data.roleType
        console.log('this.temp.dqryType' + this.temp.dqryType)
        	} else {
        		 this.$message({
        		  message: response.msg,
        		  type: 'error'
        		})
        	}
    })

    this.getUploadUrl()
    this.getList()

    this.option1s = []

    fetchALLList(this.listQuery2).then(response => {
      var datas4 = response.data.items
              	for (var j = 0; j < datas4.length; j++) {
              		var id = datas4[j].id + ''
              		var name = datas4[j].tagName
              		var per = []
              		per.value = id
              		per.label = name
        this.option1s.push(per)
        this.tagOptions.push(per)
      }
    })
  },
  methods: {
    getList() {
      this.listLoading = true// 显示加载动画
      fetchList(this.listQuery).then(response => {
        this.list = response.data.items
					 	this.total = response.data.total
        this.listLoading = false
      })
    }, getUploadUrl() {
      getUploadUrl().then(res => {
        if (res.code != 20000) {
          this.$message({
            message: res.msg,
            type: 'error'
          })
        }
        this.uploadUrl = res.msg
      })
    },
    handleAvatarSuccess(res, file) {
      this.temp.imageFront = res.data.endixUrl
    },
    handleAvatarSuccess2(res, file) {
    	this.temp.imageBack = res.data.endixUrl
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isLt2M) {
        this.$message.error(this.$t('userInfo.validation.fileSizeLimit'))
        return false
      }
      return true
    },
    handleFilter() {
      if (this.valueGmtCreate) {
        this.listQuery.gmtCreateSearchBegin = parseTime(new Date(this.valueGmtCreate[0]), '{y}-{m}-{d} {h}:{i}:{s}')
        this.listQuery.gmtCreateSearchEnd = parseTime(new Date(this.valueGmtCreate[1]), '{y}-{m}-{d} {h}:{i}:{s}')
      } else {
        this.listQuery.gmtCreateSearchBegin = undefined
        this.listQuery.gmtCreateSearchEnd = undefined
      }
      this.listQuery.page = 1
      this.getList()
    },
    sortChange(data) {
      const { prop, order } = data
					 if (prop === 'gmtCreate') {
        this.sortByGmtCreate(order)
					 }
    }, // 重置密码页面
    handleResetPassword(row) {
      this.temp = Object.assign({}, row) // copy obj
      this.temp.password = ''
      this.dialogFormResetPasswordVisible = true
      this.$nextTick(() => {
			  this.$refs['dataPassForm'].clearValidate()
      })
    }, removeContactInformation(item) {
      var index = this.temp.contactInformations.indexOf(item)
      if (index !== -1) {
        this.temp.contactInformations.splice(index, 1)
      }
    },
    addContactInformation() {
      this.temp.contactInformations.push({
        value: '',
        key: Date.now()
      })
    },
    resetPassword() {
			    resetPassword(this.temp.id, this.temp.password).then(result => {
        if (result.code == 20000) {
							 this.$notify({
							  title: this.$t('userInfo.notifications.success'),
							  message: this.$t('userInfo.notifications.passwordResetSuccess'),
							  type: 'success',
							  duration: 2000
          })
          this.dialogFormResetPasswordVisible = false
        } else {
          this.$message.error(response.message)
        }
			    })
    }, handleExport() {
					 exportUserInfoExcel(this.listQuery).then(res => {
						 window.open(Setting.base_url + 'fileserver/' + res.data.fileUrl, '_blank')
					 })
    },
    sortByGmtCreate(order) {
      if (order === 'ascending') {
        this.listQuery.sort = '+gmtCreate'
      } else {
        this.listQuery.sort = '-gmtCreate'
      }
      this.handleFilter()
    },
    resetTemp() {
      this.temp = {
        id: undefined,
        surname: '',
        name: '',
        fullname: '',
        userName: '',
        email: '',
        tel: '',
        country: '',
        province: '',
        city: '',
        gender: '',
        adress: '',
        nickname: '',
        birthday: '',
        identityNum: '',
        imageFront: '',
        password: '',
        imageBack: '',
        invitationCode: '',
        amountMain: '',
        amountSub: '',
        userType: '',
        parentId: '',
        pinyin: '',
        remark: '',
        isAgent: '',
        rekebackRule: [],
        roles: [],
        isAvailable: undefined
      }
    },
    handleCreate() {
      this.resetTemp()
      this.temp.gender = '1'
      this.temp.password = 'abc123'
      this.dialogFormAddVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    createData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          createUserInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: this.$t('userInfo.notifications.success'),
                message: this.$t('userInfo.notifications.operSuccess'),
                type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
				 updateIsAvailable(row) {
				 updateIsAvailable(row.id, row.isAgent).then(response => {
					 if (response.code != 20000) {
							 this.$message({
							  message: response.message,
							  type: 'error'
          })
					 }
      })
    },
    updateData() {
      this.$refs['dataForm2'].validate((valid) => {
        if (valid) {
          this.temp.rekebackRule = this.rekebackRule2
          updateUserInfo(this.temp).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: this.$t('userInfo.notifications.success'),
                message: this.$t('userInfo.notifications.operSuccess'),
                type: 'success',
                duration: 2000
              })
              this.getList()
              this.dialogFormAddVisible2 = false
            } else {
              this.$message.error(response.msg)
            }
          })
        }
      })
				 },
    handleUpdate(row) {
      this.temp = Object.assign({}, row)

      // this.temp.birthday= parseTime(new Date(row.birthday),'{y}-{m}-{d}' );
      this.srcList = [this.temp.imageFront, this.temp.imageBack]
      this.dialogFormEditVisible = true
      this.$nextTick(() => {
        this.$refs['dataEditForm'].clearValidate()
      })
    },	handleEdit(row) {
      this.temp = Object.assign({}, row)
      const birthdayDate = new Date(row.birthday)
      this.temp.birthday = birthdayDate.getFullYear() + '-' + 
        String(birthdayDate.getMonth() + 1).padStart(2, '0') + '-' + 
        String(birthdayDate.getDate()).padStart(2, '0')
      this.rekebackRule2 = []
      var datas = eval(row.backup5)

      for (var m = 0; m < datas.length; m++) {
        this.rekebackRule2.push(datas[m])
      }
      this.temp.gender = row.gender + ''
      this.dialogFormAddVisible2 = true
      this.$nextTick(() => {
        this.$refs['dataForm2'].clearValidate()
      })
    },
				 handleDelete(row) {
      this.$confirm(this.$t('userInfo.deleteConfirmMessage'), this.$t('userInfo.deleteConfirmTitle'), {
				 		confirmButtonText: this.$t('table.confirm'),
        cancelButtonText: this.$t('table.cancel'),
        type: 'warning'
      })
        .then(async() => {
				 		await removeUserInfo(row.id).then(result => {
            if (result.code == 20000) {
              this.$notify({
                title: this.$t('userInfo.notifications.success'),
                message: this.$t('userInfo.notifications.deleteSuccess'),
                type: 'success',
                duration: 2000
              })
              const index = this.list.indexOf(row)
				 				this.list.splice(index, 1)
            } else {
              this.$message.error(result.msg)
            }
          })
        })
        .catch(err => { console.error(err) })
    }
  }
}
</script>
<style>
	.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
  .avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
</style>
