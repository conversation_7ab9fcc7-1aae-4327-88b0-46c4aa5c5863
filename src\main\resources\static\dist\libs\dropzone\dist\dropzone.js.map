{"mappings": ";;;;;;;;;;;;;;;;;;;;ICIqB,wCAAO,iBAAb,QAAQ;;aAAF,wCAAO;+CAAP,wCAAO;;kCAAP,wCAAO;;YAC1B,EAAwC,AAAxC,sCAAwC;YACxC,GAAE,EAAF,CAAE;mBAAF,QAAQ,CAAR,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC;gBACb,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;gBAAA,CAAC;gBACvC,EAAkC,AAAlC,gCAAkC;gBAClC,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,GACxB,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,CAAC;gBAE7B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,EAAE;gBAC9B,MAAM,CAAC,IAAI;YACb,CAAC;;;YAED,GAAI,EAAJ,CAAI;mBAAJ,QAAQ,CAAR,IAAI,CAAC,KAAK,EAAW,CAAC;gBAAV,GAAG,CAAH,GAAO,CAAP,IAAO,GAAP,SAAO,CAAP,MAAO,EAAJ,IAAI,GAAP,GAAO,OAAP,IAAO,GAAP,CAAO,GAAP,IAAO,GAAP,CAAO,GAAP,CAAO,GAAP,IAAO,GAAP,CAAO,EAAP,IAAO,GAAP,IAAO,EAAP,IAAO,GAAP,CAAC;oBAAE,IAAI,CAAP,IAAO,GAAP,CAAO,IAAP,SAAO,CAAP,IAAO;gBAAD,CAAC;gBACjB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;gBAAA,CAAC;gBACvC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;oBAG9B,yBAAY,SAAZ,iBAAY,UAAZ,cAAY;gBADnB,EAAE,EAAE,SAAS;oBACX,GAAG,KAAE,SAAY,GAAI,SAAS,qBAAzB,KAAY,IAAZ,yBAAY,IAAZ,KAAY,GAAZ,SAAY,gBAAZ,yBAAY;wBAAZ,GAAG,CAAC,QAAQ,GAAZ,KAAY;wBACf,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;;;oBADtB,iBAAY;oBAAZ,cAAY;;;6BAAZ,yBAAY,IAAZ,SAAY;4BAAZ,SAAY;;;4BAAZ,iBAAY;kCAAZ,cAAY;;;;gBAInB,EAAoC,AAApC,kCAAoC;gBACpC,EAAE,EAAE,IAAI,CAAC,OAAO,EACd,IAAI,CAAC,OAAO,CAAC,aAAa,CACxB,IAAI,CAAC,SAAS,CAAC,CAAW,aAAG,KAAK,EAAE,CAAC;oBAAC,IAAI,EAAE,IAAI;gBAAC,CAAC;gBAGtD,MAAM,CAAC,IAAI;YACb,CAAC;;;YAED,GAAS,EAAT,CAAS;mBAAT,QAAQ,CAAR,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,CAAC;gBAC5B,GAAG,CAAC,MAAM,GAAG,CAAC;oBAAC,OAAO,EAAE,IAAI;oBAAE,UAAU,EAAE,IAAI;oBAAE,MAAM,EAAE,MAAM;gBAAC,CAAC;gBAEhE,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,WAAW,KAAK,CAAU,WAC1C,MAAM,CAAC,GAAG,CAAC,WAAW,CAAC,SAAS,EAAE,MAAM;qBACnC,CAAC;oBACN,EAAgB,AAAhB,cAAgB;oBAChB,EAA2E,AAA3E,yEAA2E;oBAC3E,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAa;oBAC5C,GAAG,CAAC,eAAe,CACjB,SAAS,EACT,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,MAAM;oBAEf,MAAM,CAAC,GAAG;gBACZ,CAAC;YACH,CAAC;;;YAED,EAA0E,AAA1E,wEAA0E;YAC1E,EAAwE,AAAxE,sEAAwE;YACxE,EAAmC,AAAnC,iCAAmC;YACnC,GAAG,EAAH,CAAG;mBAAH,QAAQ,CAAR,GAAG,CAAC,KAAK,EAAE,EAAE,EAAE,CAAC;gBACd,EAAE,GAAG,IAAI,CAAC,UAAU,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC/C,IAAI,CAAC,UAAU,GAAG,CAAC;oBAAA,CAAC;oBACpB,MAAM,CAAC,IAAI;gBACb,CAAC;gBAED,EAAiB,AAAjB,eAAiB;gBACjB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK;gBACrC,EAAE,GAAG,SAAS,EACZ,MAAM,CAAC,IAAI;gBAGb,EAAsB,AAAtB,oBAAsB;gBACtB,EAAE,EAAE,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC3B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;oBAC5B,MAAM,CAAC,IAAI;gBACb,CAAC;gBAED,EAA0B,AAA1B,wBAA0B;gBAC1B,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;oBAC1C,GAAG,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC;oBAC1B,EAAE,EAAE,QAAQ,KAAK,EAAE,EAAE,CAAC;wBACpB,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;wBACrB,KAAK;oBACP,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,IAAI;YACb,CAAC;;;WAhFkB,wCAAO;;;;;;AEJ5B,yBAAc,GAAG,CAAkB;;;ADGnC,GAAG,CAAC,oCAAc,GAAG,CAAC;IACpB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,GAAG,EAAE,IAAI;IAET,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,MAAM,EAAE,CAAM;IAEd,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,KAAK;IAEtB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,OAAO,EAAE,IAAI;IAEb,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,eAAe,EAAE,CAAC;IAElB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,cAAc,EAAE,KAAK;IAErB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,QAAQ,EAAE,KAAK;IAEf,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,aAAa,EAAE,KAAK;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,SAAS,EAAE,OAAe;IAE1B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,oBAAoB,EAAE,KAAK;IAE3B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,WAAW,EAAE,KAAK;IAElB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,gBAAgB,EAAE,CAAC;IAEnB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,WAAW,EAAE,GAAG;IAEhB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,SAAS,EAAE,CAAM;IAEjB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,qBAAqB,EAAE,IAAI;IAE3B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,oBAAoB,EAAE,EAAE;IAExB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,GAAG;IAEnB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,GAAG;IAEpB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,eAAe,EAAE,CAAM;IAEvB,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,WAAW,EAAE,IAAI;IAEjB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,YAAY,EAAE,IAAI;IAElB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,cAAc,EAAE,IAAI;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,aAAa,EAAE,GAAG;IAElB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,YAAY,EAAE,CAAS;IAEvB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,YAAY,EAAE,IAAI;IAElB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,QAAQ,EAAE,IAAI;IAEd,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,OAAO,EAAE,IAAI;IAEb,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,cAAc,EAAE,IAAI;IAEpB,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,SAAS,EAAE,IAAI;IAEf,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EAUG,AAVH;;;;;;;;;;GAUG,AAVH,EAUG,CACH,aAAa,EAAE,IAAI;IAEnB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EASG,AATH;;;;;;;;;GASG,AATH,EASG,CACH,gBAAgB,EAAE,IAAI;IAEtB,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,SAAS,EAAE,IAAI;IAEf,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,cAAc,EAAE,KAAK;IAErB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,iBAAiB,EAAE,IAAI;IAEvB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,eAAe,EAAE,KAAK;IAEtB,EAMG,AANH;;;;;;GAMG,AANH,EAMG,CACH,oBAAoB,EAAE,CAAM;IAE5B,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CACH,OAAO,EAAE,IAAI;IAEb,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,IAAI;IAEpB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,UAAU,EAAE,IAAI;IAEhB,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,aAAa,EAAE,KAAK;IAEpB,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,kBAAkB,EAAE,CAA2B;IAE/C,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,mBAAmB,EACjB,CAAyD;IAE3D,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,gBAAgB,EACd,CAAiF;IAEnF,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,cAAc,EACZ,CAAsE;IAExE,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,mBAAmB,EAAE,CAAsC;IAE3D,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,CAA4C;IAE/D,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,gBAAgB,EAAE,CAAe;IAEjC,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,kBAAkB,EAAE,CAAkB;IAEtC,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,4BAA4B,EAAE,CAA8C;IAE5E,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,cAAc,EAAE,CAAa;IAE7B,EAEG,AAFH;;GAEG,AAFH,EAEG,CACH,0BAA0B,EAAE,IAAI;IAEhC,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,oBAAoB,EAAE,CAAoC;IAE1D,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,iBAAiB,EAAE,CAAC;QAAC,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,EAAE,EAAE,CAAI;QAAE,CAAC,EAAE,CAAG;IAAC,CAAC;IACrE,EAGG,AAHH;;;GAGG,AAHH,EAGG,CACH,IAAI,EAAJ,QAAQ,GAAD,CAAC;IAAA,CAAC;IAET,EASG,AATH;;;;;;;;;GASG,AATH,EASG,CACH,MAAM,EAAN,QAAQ,CAAD,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;QACzB,EAAE,EAAE,KAAK,EACP,MAAM,CAAC,CAAC;YACN,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI;YAC9B,YAAY,EAAE,KAAK,CAAC,KAAK;YACzB,eAAe,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;YAChC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;YACnC,iBAAiB,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe;YACpD,iBAAiB,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS;QACzD,CAAC;IAEL,CAAC;IAED,EAQG,AARH;;;;;;;;GAQG,AARH,EAQG,CACH,MAAM,EAAN,QAAQ,CAAD,IAAI,EAAE,IAAI,EAAE,CAAC;QAClB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,cAAc,EAAE,QAAQ,CAAxB,cAAc,CAAY,IAAI,EAAE,IAAI,EAAE,CAAC;QACrC,IAAI;IACN,CAAC;IAED,EAKG,AALH;;;;;GAKG,AALH,EAKG,CACH,UAAU,EAAE,KAAK;IAEjB,EAIG,AAJH;;;;GAIG,AAJH,EAIG,CACH,QAAQ,EAAR,QAAQ,GAAG,CAAC;QACV,EAAqC,AAArC,mCAAqC;QACrC,GAAG,CAAC,cAAc;QAClB,IAAI,CAAC,OAAO,CAAC,SAAS,GAAI,CAAA,EAAyB,MAAyB,CAAhD,IAAI,CAAC,OAAO,CAAC,SAAS,EAAC,CAAyB;YAEvE,yBAAS,SAAT,iBAAS,UAAT,cAAS;;YAAd,GAAG,KAAE,SAAS,GAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAK,0BAApD,KAAS,IAAT,yBAAS,IAAT,KAAS,GAAT,SAAS,gBAAT,yBAAS;gBAAT,GAAG,CAAC,KAAK,GAAT,KAAS;gBACZ,EAAE,yBAAyB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC;oBACjD,cAAc,GAAG,KAAK;oBACtB,KAAK,CAAC,SAAS,GAAG,CAAY,YAAE,CAAiC,AAAjC,EAAiC,AAAjC,+BAAiC;oBACjE,KAAK;gBACP,CAAC;;;YALE,iBAAS;YAAT,cAAS;;;qBAAT,yBAAS,IAAT,SAAS;oBAAT,SAAS;;;oBAAT,iBAAS;0BAAT,cAAS;;;;QAOd,EAAE,GAAG,cAAc,EAAE,CAAC;YACpB,cAAc,GAAG,wCAAQ,CAAC,aAAa,CACrC,CAA6C;YAE/C,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,cAAc;QACzC,CAAC;QAED,GAAG,CAAC,IAAI,GAAG,cAAc,CAAC,oBAAoB,CAAC,CAAM,OAAE,CAAC;QACxD,EAAE,EAAE,IAAI,EAAE,CAAC;YACT,EAAE,EAAE,IAAI,CAAC,WAAW,IAAI,IAAI,EAC1B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;iBAC9C,EAAE,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI,EAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;QAErD,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe;IACtD,CAAC;IAED,EAWG,AAXH;;;;;;;;;;;GAWG,AAXH,EAWG,CACH,MAAM,EAAN,QAAQ,CAAD,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,CAAC;QACzC,GAAG,CAAC,IAAI,GAAG,CAAC;YACV,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,IAAI,CAAC,KAAK;YACpB,SAAS,EAAE,IAAI,CAAC,MAAM;QACxB,CAAC;QAED,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM;QAEvC,EAAsD,AAAtD,oDAAsD;QACtD,EAAE,EAAE,KAAK,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACpC,KAAK,GAAG,IAAI,CAAC,QAAQ;YACrB,MAAM,GAAG,IAAI,CAAC,SAAS;QACzB,CAAC,MAAM,EAAE,EAAE,KAAK,IAAI,IAAI,EACtB,KAAK,GAAG,MAAM,GAAG,QAAQ;aACpB,EAAE,EAAE,MAAM,IAAI,IAAI,EACvB,MAAM,GAAG,KAAK,GAAG,QAAQ;QAG3B,EAAmC,AAAnC,iCAAmC;QACnC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ;QACrC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS;QAExC,GAAG,CAAC,QAAQ,GAAG,KAAK,GAAG,MAAM;QAE7B,EAAE,EAAE,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;YACrD,EAAsC,AAAtC,oCAAsC;YACtC,EAAE,EAAE,YAAY,KAAK,CAAM;gBACzB,EAAE,EAAE,QAAQ,GAAG,QAAQ,EAAE,CAAC;oBACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM;oBAC5B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,GAAG,QAAQ;gBAC3C,CAAC,MAAM,CAAC;oBACN,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK;oBAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,QAAQ;gBAC3C,CAAC;mBACI,EAAE,EAAE,YAAY,KAAK,CAAS;gBACnC,EAAmB,AAAnB,iBAAmB;gBACnB,EAAE,EAAE,QAAQ,GAAG,QAAQ,EACrB,MAAM,GAAG,KAAK,GAAG,QAAQ;qBAEzB,KAAK,GAAG,MAAM,GAAG,QAAQ;mBAG3B,KAAK,CAAC,GAAG,CAAC,KAAK,CAAE,CAAsB,wBAAe,MAAC,CAAd,YAAY,EAAC,CAAC;QAE3D,CAAC;QAED,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC;QAC5C,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,IAAI,CAAC;QAE9C,IAAI,CAAC,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAC,SAAS,GAAG,MAAM;QAEvB,MAAM,CAAC,IAAI;IACb,CAAC;IAED,EAQG,AARH;;;;;;;;GAQG,AARH,EAQG,CACH,aAAa,EAAb,QAAQ,CAAM,IAAI,EAAE,IAAI,EAAE,CAAC;QACzB,EAAE,GACC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KACtD,IAAI,CAAC,IAAI,CAAC,KAAK,aAEf,MAAM,CAAC,IAAI,CAAC,WAAW,CACrB,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,WAAW,EACxB,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI,CAAC,OAAO,CAAC,YAAY,EACzB,IAAI;aAGN,MAAM,CAAC,IAAI,CAAC,IAAI;IAEpB,CAAC;IAED,EAaG,AAbH;;;;;;;;;;;;;GAaG,AAbH,EAaG,CACH,eAAe,EAAE,gEAAsB;IAEvC,EAOG,AAPH;;;;;;;GAOG,AAPH,EAOG,CAEH,EAA+D,AAA/D,6DAA+D;IAC/D,IAAI,EAAJ,QAAQ,CAAH,CAAC,EAAE,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IACD,SAAS,EAAT,QAAQ,CAAE,CAAC,EAAE,CAAC;IAAA,CAAC;IACf,OAAO,EAAP,QAAQ,CAAA,CAAC,EAAE,CAAC;QACV,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IACD,SAAS,EAAT,QAAQ,CAAE,CAAC,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;IACnD,CAAC;IACD,QAAQ,EAAR,QAAQ,CAAC,CAAC,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;IACnD,CAAC;IACD,SAAS,EAAT,QAAQ,CAAE,CAAC,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAe;IACtD,CAAC;IAED,KAAK,EAAL,QAAQ,CAAF,CAAC,EAAE,CAAC;IAAA,CAAC;IAEX,EAA2E,AAA3E,yEAA2E;IAC3E,EAA2D,AAA3D,yDAA2D;IAC3D,KAAK,EAAL,QAAQ,GAAA,CAAC;QACP,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAY;IACnD,CAAC;IAED,EAA2C,AAA3C,yCAA2C;IAC3C,EAAkB,AAAlB,gBAAkB;IAClB,SAAS,EAAT,QAAQ,CAAE,IAAI,EAAE,CAAC;QACf,EAAE,EAAE,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,iBAAiB,EACzC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAY;QAGzC,EAAE,EAAE,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;;YAC5D,IAAI,CAAC,cAAc,GAAG,wCAAQ,CAAC,aAAa,CAC1C,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI;YAEnC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAE,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;YAEtE,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc;gBACjD,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;gBAAb,GAAG,KAAE,SAAQ,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAgB,qCAAjE,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;oBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;oBACX,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI;;;gBADzB,iBAAQ;gBAAR,cAAQ;;;yBAAR,yBAAQ,IAAR,SAAQ;wBAAR,SAAQ;;;wBAAR,iBAAQ;8BAAR,cAAQ;;;;gBAGR,0BAAI,SAAJ,kBAAI,UAAJ,eAAI;;gBAAT,GAAG,KAAE,UAAI,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAgB,qCAA7D,MAAI,IAAJ,0BAAI,IAAJ,MAAI,GAAJ,UAAI,gBAAJ,0BAAI;oBAAJ,IAAI,GAAJ,MAAI;oBACP,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;;;gBADrC,kBAAI;gBAAJ,eAAI;;;yBAAJ,0BAAI,IAAJ,UAAI;wBAAJ,UAAI;;;wBAAJ,kBAAI;8BAAJ,eAAI;;;;YAIT,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;gBAChC,IAAI,CAAC,WAAW,GAAG,wCAAQ,CAAC,aAAa,CACtC,CAAiE,uEAA8B,MAAI,CAAhC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAC,CAAI;gBAEtG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW;YAClD,CAAC;YAED,GAAG,CAAC,eAAe,GAAG,QAAQ,CAAP,CAAC,EAAK,CAAC;;gBAC5B,CAAC,CAAC,cAAc;gBAChB,CAAC,CAAC,eAAe;gBACjB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EACpC,MAAM,CAAC,wCAAQ,CAAC,OAAO,OAChB,OAAO,CAAC,4BAA4B,EACzC,QAAQ;oBAAF,MAAM,QAAD,UAAU,CAAC,IAAI;;qBAEvB,CAAC;;oBACN,EAAE,QAAO,OAAO,CAAC,0BAA0B,EACzC,MAAM,CAAC,wCAAQ,CAAC,OAAO,OAChB,OAAO,CAAC,0BAA0B,EACvC,QAAQ;wBAAF,MAAM,QAAD,UAAU,CAAC,IAAI;;yBAG5B,MAAM,OAAM,UAAU,CAAC,IAAI;gBAE/B,CAAC;YACH,CAAC;gBAEI,0BAAc,SAAd,kBAAc,UAAd,eAAc;;gBAAnB,GAAG,KAAE,UAAc,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACzD,CAAkB,uCADf,MAAc,IAAd,0BAAc,IAAd,MAAc,GAAd,UAAc,gBAAd,0BAAc;oBAAd,GAAG,CAAC,UAAU,GAAd,MAAc;oBAGjB,UAAU,CAAC,gBAAgB,CAAC,CAAO,QAAE,eAAe;;;gBAHjD,kBAAc;gBAAd,eAAc;;;yBAAd,0BAAc,IAAd,UAAc;wBAAd,UAAc;;;wBAAd,kBAAc;8BAAd,eAAc;;;;QAKrB,CAAC;IACH,CAAC;IAED,EAAqC,AAArC,mCAAqC;IACrC,WAAW,EAAX,QAAQ,CAAI,IAAI,EAAE,CAAC;QACjB,EAAE,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,EACvE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc;QAEhE,MAAM,CAAC,IAAI,CAAC,2BAA2B;IACzC,CAAC;IAED,EAA6C,AAA7C,2CAA6C;IAC7C,EAAgC,AAAhC,8BAAgC;IAChC,SAAS,EAAT,QAAQ,CAAE,IAAI,EAAE,OAAO,EAAE,CAAC;QACxB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAiB;gBACjD,yBAAoB,SAApB,iBAAoB,UAApB,cAAoB;;gBAAzB,GAAG,KAAE,SAAoB,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAC/D,CAAqB,0CADlB,KAAoB,IAApB,yBAAoB,IAApB,KAAoB,GAApB,SAAoB,gBAApB,yBAAoB,QAEtB,CAAC;oBAFC,GAAG,CAAC,gBAAgB,GAApB,KAAoB;oBAGvB,gBAAgB,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;oBAChC,gBAAgB,CAAC,GAAG,GAAG,OAAO;gBAChC,CAAC;;gBALI,iBAAoB;gBAApB,cAAoB;;;yBAApB,yBAAoB,IAApB,SAAoB;wBAApB,SAAoB;;;wBAApB,iBAAoB;8BAApB,cAAoB;;;;YAOzB,MAAM,CAAC,UAAU,CACf,QAAQ;gBAAF,MAAM,CAAN,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAkB;eAC1D,CAAC;QAEL,CAAC;IACH,CAAC;IAED,EAAkC,AAAlC,gCAAkC;IAClC,EAAgC,AAAhC,8BAAgC;IAChC,KAAK,EAAL,QAAQ,CAAF,IAAI,EAAE,OAAO,EAAE,CAAC;QACpB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAU;YAC5C,EAAE,EAAE,MAAM,CAAC,OAAO,KAAK,CAAQ,WAAI,OAAO,CAAC,KAAK,EAC9C,OAAO,GAAG,OAAO,CAAC,KAAK;gBAEpB,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;gBAAb,GAAG,KAAE,SAAQ,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACnD,CAAwB,6CADrB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;oBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;oBAGX,IAAI,CAAC,WAAW,GAAG,OAAO;;;gBAHvB,iBAAQ;gBAAR,cAAQ;;;yBAAR,yBAAQ,IAAR,SAAQ;wBAAR,SAAQ;;;wBAAR,iBAAQ;8BAAR,cAAQ;;;;QAKf,CAAC;IACH,CAAC;IAED,aAAa,EAAb,QAAQ,GAAQ,CAAC;IAAA,CAAC;IAElB,EAAyE,AAAzE,uEAAyE;IACzE,EAAmC,AAAnC,iCAAmC;IACnC,EAAkB,AAAlB,gBAAkB;IAClB,UAAU,EAAV,QAAQ,CAAG,IAAI,EAAE,CAAC;QAChB,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAe;YACjD,EAAE,EAAE,IAAI,CAAC,WAAW,EAClB,MAAM,CAAE,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;QAEtE,CAAC;IACH,CAAC;IAED,kBAAkB,EAAlB,QAAQ,GAAa,CAAC;IAAA,CAAC;IAEvB,EAAoD,AAApD,kDAAoD;IACpD,EAAkE,AAAlE,gEAAkE;IAClE,EAAgE,AAAhE,8DAAgE;IAChE,cAAc,EAAd,QAAQ,CAAO,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,CAAC;YAElC,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;QADf,EAAE,EAAE,IAAI,CAAC,cAAc;YACrB,GAAG,KAAE,SAAQ,GAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,CACnD,CAA0B,+CADvB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;gBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;gBAGX,IAAI,CAAC,QAAQ,KAAK,CAAU,YACvB,IAAI,CAAC,KAAK,GAAG,QAAQ,GACrB,IAAI,CAAC,KAAK,CAAC,KAAK,GAAI,CAAA,EAAW,MAAC,CAAV,QAAQ,EAAC,CAAC;;;YALlC,iBAAQ;YAAR,cAAQ;;;qBAAR,yBAAQ,IAAR,SAAQ;oBAAR,SAAQ;;;oBAAR,iBAAQ;0BAAR,cAAQ;;;;IAQjB,CAAC;IAED,EAA0D,AAA1D,wDAA0D;IAC1D,EAAyE,AAAzE,uEAAyE;IACzE,mBAAmB,EAAnB,QAAQ,GAAc,CAAC;IAAA,CAAC;IAExB,EAAuE,AAAvE,qEAAuE;IACvE,EAA0E,AAA1E,wEAA0E;IAC1E,EAAmD,AAAnD,iDAAmD;IACnD,OAAO,EAAP,QAAQ,GAAE,CAAC;IAAA,CAAC;IAEZ,eAAe,EAAf,QAAQ,GAAU,CAAC;IAAA,CAAC;IAEpB,EAAsD,AAAtD,oDAAsD;IACtD,EAAkB,AAAlB,gBAAkB;IAClB,OAAO,EAAP,QAAQ,CAAA,IAAI,EAAE,CAAC;QACb,EAAE,EAAE,IAAI,CAAC,cAAc,EACrB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAY;IAEzD,CAAC;IAED,eAAe,EAAf,QAAQ,GAAU,CAAC;IAAA,CAAC;IAEpB,EAA+B,AAA/B,6BAA+B;IAC/B,QAAQ,EAAR,QAAQ,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;IACjE,CAAC;IAED,gBAAgB,EAAhB,QAAQ,GAAW,CAAC;IAAA,CAAC;IAErB,EAAgE,AAAhE,8DAAgE;IAChE,EAAkB,AAAlB,gBAAkB;IAClB,QAAQ,EAAR,QAAQ,CAAC,IAAI,EAAE,CAAC;QACd,EAAE,EAAE,IAAI,CAAC,WAAW,EAClB,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;QAE1D,EAAE,EAAE,IAAI,CAAC,cAAc,EACrB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAa;IAE1D,CAAC;IAED,gBAAgB,EAAhB,QAAQ,GAAW,CAAC;IAAA,CAAC;IAErB,gBAAgB,EAAhB,QAAQ,GAAW,CAAC;IAAA,CAAC;IAErB,eAAe,EAAf,QAAQ,GAAU,CAAC;IAAA,CAAC;IAEpB,aAAa,EAAb,QAAQ,GAAQ,CAAC;IAAA,CAAC;IAElB,UAAU,EAAV,QAAQ,GAAK,CAAC;IAAA,CAAC;AACjB,CAAC;IAED,wCAA8B,GAAf,oCAAc;;;IFhxBR,wCAAQ,iBAAd,QAAQ;;+BAAF,wCAAQ;aAAR,wCAAQ,CAgDf,EAAE,EAAE,OAAO;+CAhDJ,wCAAQ;;mGAAR,wCAAQ;QAkDzB,GAAG,CAAC,QAAQ,EAAE,IAAI;cACb,OAAO,GAAG,EAAE;cAEZ,iBAAiB,GAAG,CAAC,CAAC;cACtB,SAAS,GAAG,CAAC,CAAC;cACd,KAAK,GAAG,CAAC,CAAC,CAAE,CAAY,AAAZ,EAAY,AAAZ,UAAY;QAE7B,EAAE,EAAE,MAAM,OAAM,OAAO,KAAK,CAAQ,eAC7B,OAAO,GAAG,QAAQ,CAAC,aAAa,OAAM,OAAO;QAGpD,EAAmF,AAAnF,iFAAmF;QACnF,EAAE,SAAQ,OAAO,UAAS,OAAO,CAAC,QAAQ,IAAI,IAAI,EAChD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAA2B;QAG7C,EAAE,QAAO,OAAO,CAAC,QAAQ,EACvB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAA4B;QAG9C,EAA0C,AAA1C,wCAA0C;QAC1C,wCAAQ,CAAC,SAAS,CAAC,IAAI;QAEvB,EAA8C,AAA9C,4CAA8C;cACzC,OAAO,CAAC,QAAQ;QAErB,GAAG,CAAC,cAAc,IACf,IAAI,GAAG,wCAAQ,CAAC,iBAAiB,OAAM,OAAO,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC;QAAA,CAAC;cAElE,OAAO,GAAG,yCAAM,CACnB,IAAI,EACJ,CAAC;QAAA,CAAC,EACF,wCAAc,EACd,cAAc,EACd,OAAO,IAAI,IAAI,GAAG,OAAO,GAAG,CAAC;QAAA,CAAC;cAG3B,OAAO,CAAC,eAAe,SAAQ,OAAO,CAAC,eAAe,CAAC,OAAO,SAEjE,CAAE;QAGJ,EAA0D,AAA1D,wDAA0D;QAC1D,EAAE,QAAO,OAAO,CAAC,aAAa,KAAK,wCAAQ,CAAC,kBAAkB,IAC5D,MAAM,0DAAM,OAAO,CAAC,QAAQ,CAAC,IAAI;QAGnC,EAAqE,AAArE,mEAAqE;QACrE,EAAE,QAAO,OAAO,CAAC,GAAG,IAAI,IAAI,QACrB,OAAO,CAAC,GAAG,SAAQ,OAAO,CAAC,YAAY,CAAC,CAAQ;QAGvD,EAAE,SAAQ,OAAO,CAAC,GAAG,EACnB,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAkB;QAGpC,EAAE,QAAO,OAAO,CAAC,aAAa,UAAS,OAAO,CAAC,iBAAiB,EAC9D,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAoG;QAIxG,EAAE,QAAO,OAAO,CAAC,cAAc,UAAS,OAAO,CAAC,QAAQ,EACtD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAmD;QAGrE,EAAE,QAAO,OAAO,CAAC,UAAU,UAAS,OAAO,CAAC,cAAc,EACxD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAqD;QAGvE,EAA0B,AAA1B,wBAA0B;QAC1B,EAAE,QAAO,OAAO,CAAC,iBAAiB,EAAE,CAAC;kBAC9B,OAAO,CAAC,aAAa,SAAQ,OAAO,CAAC,iBAAiB;YAC3D,MAAM,OAAM,OAAO,CAAC,iBAAiB;QACvC,CAAC;QAED,EAA0B,AAA1B,wBAA0B;QAC1B,EAAE,QAAO,OAAO,CAAC,cAAc,IAAI,IAAI,QAChC,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAP,IAAI;YAC7B,MAAM,OAAD,OAAO,CAAC,cAAc,CAAC,IAAI,iDAAO,IAAI,CAAC,IAAI,EAAE,IAAI;;QAG1D,EAAE,EAAE,MAAM,OAAM,OAAO,CAAC,MAAM,KAAK,CAAQ,eACpC,OAAO,CAAC,MAAM,SAAQ,OAAO,CAAC,MAAM,CAAC,WAAW;QAGvD,EAAE,GAAG,QAAQ,SAAQ,mBAAmB,OAAO,QAAQ,CAAC,UAAU,EAChE,EAAsB,AAAtB,oBAAsB;QACtB,QAAQ,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ;QAG1C,EAA2G,AAA3G,yGAA2G;QAC3G,EAAE,QAAO,OAAO,CAAC,iBAAiB,KAAK,KAAK;YAC1C,EAAE,QAAO,OAAO,CAAC,iBAAiB,QAC3B,iBAAiB,GAAG,wCAAQ,CAAC,UAAU,OACrC,OAAO,CAAC,iBAAiB,EAC9B,CAAmB;uBAGhB,iBAAiB,SAAQ,OAAO;;QAIzC,EAAE,QAAO,OAAO,CAAC,SAAS;YACxB,EAAE,QAAO,OAAO,CAAC,SAAS,KAAK,IAAI,QAC5B,iBAAiB,GAAG,CAAC;sBAAK,OAAO;YAAA,CAAC;uBAElC,iBAAiB,GAAG,wCAAQ,CAAC,WAAW,OACtC,OAAO,CAAC,SAAS,EACtB,CAAW;;cAKZ,IAAI;;;kCApKQ,wCAAQ;;YAuK3B,EAA4C,AAA5C,0CAA4C;YAC5C,GAAgB,EAAhB,CAAgB;mBAAhB,QAAQ,CAAR,gBAAgB,GAAG,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI,CAAC,QAAQ;mBAAE,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI;;YACtE,CAAC;;;YAED,EAA4C,AAA5C,0CAA4C;YAC5C,EAAuE,AAAvE,qEAAuE;YACvE,GAAgB,EAAhB,CAAgB;mBAAhB,QAAQ,CAAR,gBAAgB,GAAG,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,EAAL,IAAI,CAAC,QAAQ;mBAAE,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI;;YACvE,CAAC;;;YAED,GAAkB,EAAlB,CAAkB;mBAAlB,QAAQ,CAAR,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CACd,MAAM,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI,CAAC,MAAM,KAAK,MAAM;mBACvC,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI;;YACvB,CAAC;;;YAED,EAA0C,AAA1C,wCAA0C;YAC1C,GAAc,EAAd,CAAc;mBAAd,QAAQ,CAAR,cAAc,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,MAAM;YAChD,CAAC;;;YAED,GAAiB,EAAjB,CAAiB;mBAAjB,QAAQ,CAAR,iBAAiB,GAAG,CAAC;gBACnB,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,SAAS;YACnD,CAAC;;;YAED,GAAa,EAAb,CAAa;mBAAb,QAAQ,CAAR,aAAa,GAAG,CAAC;gBACf,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,wCAAQ,CAAC,KAAK;YAC/C,CAAC;;;YAED,EAA4C,AAA5C,0CAA4C;YAC5C,GAAc,EAAd,CAAc;mBAAd,QAAQ,CAAR,cAAc,GAAG,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CACd,MAAM,CACL,QAAQ,CAAP,IAAI;oBACH,MAAM,CAAN,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,MAAM;mBAExE,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI;;YACvB,CAAC;;;YAED,EAAkE,AAAlE,gEAAkE;YAClE,EAA+D,AAA/D,6DAA+D;YAC/D,GAAI,EAAJ,CAAI;mBAAJ,QAAQ,CAAR,IAAI,GAAG,CAAC;;gBACN,EAA+B,AAA/B,6BAA+B;gBAC/B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM,OACjC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAS,UAAE,CAAqB;gBAG5D,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAU,eACzC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAa,eAEzC,IAAI,CAAC,OAAO,CAAC,WAAW,CACtB,wCAAQ,CAAC,aAAa,CACnB,CAA2E,mFAAkC,MAAe,CAA/C,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAC,CAAe;gBAKnI,EAAE,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC;;oBAClC,GAAG,CAAC,oBAAoB,GAAG,QACjC,GADuC,CAAC;;wBAChC,EAAE,QAAO,eAAe,QACjB,eAAe,CAAC,UAAU,CAAC,WAAW,OAAM,eAAe;8BAE7D,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAO;8BAChD,eAAe,CAAC,YAAY,CAAC,CAAM,OAAE,CAAM;wBAChD,EAAE,QAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,UAAS,OAAO,CAAC,QAAQ,GAAG,CAAC,QACxD,eAAe,CAAC,YAAY,CAAC,CAAU,WAAE,CAAU;8BAErD,eAAe,CAAC,SAAS,GAAG,CAAiB;wBAElD,EAAE,QAAO,OAAO,CAAC,aAAa,KAAK,IAAI,QAChC,eAAe,CAAC,YAAY,CAC/B,CAAQ,eACH,OAAO,CAAC,aAAa;wBAG9B,EAAE,QAAO,OAAO,CAAC,OAAO,KAAK,IAAI,QAC1B,eAAe,CAAC,YAAY,CAAC,CAAS,gBAAO,OAAO,CAAC,OAAO;wBAGnE,EAAqD,AAArD,mDAAqD;8BAChD,eAAe,CAAC,YAAY,CAAC,CAAU,WAAE,CAAI;wBAElD,EAAyE,AAAzE,uEAAyE;wBACzE,EAAqC,AAArC,mCAAqC;8BAChC,eAAe,CAAC,KAAK,CAAC,UAAU,GAAG,CAAQ;8BAC3C,eAAe,CAAC,KAAK,CAAC,QAAQ,GAAG,CAAU;8BAC3C,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,CAAG;8BAC/B,eAAe,CAAC,KAAK,CAAC,IAAI,GAAG,CAAG;8BAChC,eAAe,CAAC,KAAK,CAAC,MAAM,GAAG,CAAG;8BAClC,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAG;wBACtC,wCAAQ,CAAC,UAAU,OACZ,OAAO,CAAC,oBAAoB,EACjC,CAAsB,uBACtB,WAAW,OAAM,eAAe;8BAC7B,eAAe,CAAC,gBAAgB,CAAC,CAAQ,SAAE,QACxD,GAD8D,CAAC;4BACrD,GAAG,CAAa,gBAAoB,WAAf,eAAe,EAA9B,KAAK,GAAK,gBAAoB,CAA9B,KAAK;gCAEJ,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;4BADf,EAAE,EAAE,KAAK,CAAC,MAAM;gCACd,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;oCAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;4CACN,OAAO,CAAC,IAAI;;;gCADd,iBAAQ;gCAAR,cAAQ;;;yCAAR,yBAAQ,IAAR,SAAQ;wCAAR,SAAQ;;;wCAAR,iBAAQ;8CAAR,cAAQ;;;;oCAIV,IAAI,CAAC,CAAY,aAAE,KAAK;4BAC7B,oBAAoB;wBACtB,CAAC;oBACH,CAAC;oBACD,oBAAoB;gBACtB,CAAC;gBAED,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,IAAI,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS;oBAKzD,yBAAa,SAAb,iBAAa,UAAb,cAAa;;oBAHlB,EAA2D,AAA3D,yDAA2D;oBAC3D,EAA0E,AAA1E,wEAA0E;oBAC1E,EAAyC,AAAzC,uCAAyC;oBACzC,GAAG,KAAE,SAAa,GAAI,IAAI,CAAC,MAAM,qBAA5B,KAAa,IAAb,yBAAa,IAAb,KAAa,GAAb,SAAa,gBAAb,yBAAa;wBAAb,GAAG,CAAC,SAAS,GAAb,KAAa;wBAChB,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS;;;oBADtC,iBAAa;oBAAb,cAAa;;;6BAAb,yBAAa,IAAb,SAAa;4BAAb,SAAa;;;4BAAb,iBAAa;kCAAb,cAAa;;;;gBAIlB,IAAI,CAAC,EAAE,CAAC,CAAgB,iBAAE,QAAQ;oBAAF,MAAM,SAAD,yBAAyB;;gBAE9D,IAAI,CAAC,EAAE,CAAC,CAAa,cAAE,QAAQ;oBAAF,MAAM,QAAD,yBAAyB;;gBAE3D,IAAI,CAAC,EAAE,CAAC,CAAU,WAAE,QAAQ,CAAP,IAAI;oBAAK,MAAM,QAAD,IAAI,CAAC,CAAU,WAAE,IAAI;;gBAExD,EAAgE,AAAhE,8DAAgE;gBAChE,IAAI,CAAC,EAAE,CAAC,CAAU,WAAE,QAAQ,CAAP,IAAI,EAAK,CAAC;;oBAC7B,EAAE,SACK,aAAa,GAAG,MAAM,KAAK,CAAC,WAC5B,iBAAiB,GAAG,MAAM,KAAK,CAAC,WAChC,cAAc,GAAG,MAAM,KAAK,CAAC,EAElC,EAAqF,AAArF,mFAAqF;oBACrF,MAAM,CAAC,UAAU,CAAC,QAAQ;wBAAF,MAAM,SAAD,IAAI,CAAC,CAAe;uBAAG,CAAC;gBAEzD,CAAC;gBAED,GAAK,CAAC,aAAa,GAAG,QAAQ,CAAxB,aAAa,CAAa,CAAC,EAAE,CAAC;oBAClC,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,KAAK,EACtB,EAA+C,AAA/C,6CAA+C;oBAC/C,EAA8C,AAA9C,4CAA8C;oBAC9C,EAAoC,AAApC,kCAAoC;oBACpC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;wBACrD,EAAE,EAAE,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAO,QAAE,MAAM,CAAC,IAAI;oBACtD,CAAC;oBAEH,MAAM,CAAC,KAAK;gBACd,CAAC;gBAED,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAxB,aAAa,CAAa,CAAC,EAAE,CAAC;oBAChC,EAA+C,AAA/C,6CAA+C;oBAC/C,EAA+C,AAA/C,6CAA+C;oBAC/C,EAA2B,AAA3B,yBAA2B;oBAC3B,EAAE,GAAG,aAAa,CAAC,CAAC,GAAG,MAAM;oBAC7B,CAAC,CAAC,eAAe;oBACjB,EAAE,EAAE,CAAC,CAAC,cAAc,EAClB,MAAM,CAAC,CAAC,CAAC,cAAc;yBAEvB,MAAM,CAAE,CAAC,CAAC,WAAW,GAAG,KAAK;gBAEjC,CAAC;gBAED,EAAuB,AAAvB,qBAAuB;gBACvB,IAAI,CAAC,SAAS,GAAG,CAAC;oBAChB,CAAC;wBACC,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,MAAM,EAAE,CAAC;4BACP,SAAS,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCACjB,MAAM,QAAM,IAAI,CAAC,CAAW,YAAE,CAAC;4BACjC,CAAC;4BACD,SAAS,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCACjB,aAAa,CAAC,CAAC;gCACf,MAAM,QAAM,IAAI,CAAC,CAAW,YAAE,CAAC;4BACjC,CAAC;4BACD,QAAQ,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCAChB,EAA6D,AAA7D,2DAA6D;gCAC7D,EAAmG,AAAnG,iGAAmG;gCACnG,EAAiF,AAAjF,+EAAiF;gCACjF,GAAG,CAAC,IAAI;gCACR,GAAG,CAAC,CAAC;oCACH,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,aAAa;gCACrC,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;gCAAA,CAAC;gCAClB,CAAC,CAAC,YAAY,CAAC,UAAU,GACvB,CAAM,UAAK,IAAI,IAAI,CAAU,cAAK,IAAI,GAAG,CAAM,QAAG,CAAM;gCAE1D,aAAa,CAAC,CAAC;gCACf,MAAM,QAAM,IAAI,CAAC,CAAU,WAAE,CAAC;4BAChC,CAAC;4BACD,SAAS,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCACjB,MAAM,QAAM,IAAI,CAAC,CAAW,YAAE,CAAC;4BACjC,CAAC;4BACD,IAAI,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCACZ,aAAa,CAAC,CAAC;gCACf,MAAM,QAAM,IAAI,CAAC,CAAC;4BACpB,CAAC;4BACD,OAAO,EAAE,QAAQ,CAAP,CAAC,EAAK,CAAC;gCACf,MAAM,QAAM,IAAI,CAAC,CAAS,UAAE,CAAC;4BAC/B,CAAC;wBACH,CAAC;oBAMH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAP,gBAAgB,EAAK,CAAC;;oBACpD,MAAM,SAAM,SAAS,CAAC,IAAI,CAAC,CAAC;wBAC1B,OAAO,EAAE,gBAAgB;wBACzB,MAAM,EAAE,CAAC;4BACP,KAAK,EAAE,QAAQ,CAAP,GAAG,EAAK,CAAC;gCACf,EAAgF,AAAhF,8EAAgF;gCAChF,EAAE,EACA,gBAAgB,aAAU,OAAO,IACjC,GAAG,CAAC,MAAM,aAAU,OAAO,IAC3B,wCAAQ,CAAC,aAAa,CACpB,GAAG,CAAC,MAAM,UACL,OAAO,CAAC,aAAa,CAAC,CAAa,wBAGrC,eAAe,CAAC,KAAK,GAAI,CAAoB,AAApB,EAAoB,AAApB,kBAAoB;gCAEpD,MAAM,CAAC,IAAI;4BACb,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,MAAM;gBAEX,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;YACpC,CAAC;;;YAED,EAAuB,AAAvB,qBAAuB;YACvB,GAAO,EAAP,CAAO;mBAAP,QAAQ,CAAR,OAAO,GAAG,CAAC;gBACT,IAAI,CAAC,OAAO;gBACZ,IAAI,CAAC,cAAc,CAAC,IAAI;gBACxB,EAAE,EACA,IAAI,CAAC,eAAe,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,UAAU,GAAG,SAAS,EAC1E,CAAC;oBACD,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe;oBAChE,IAAI,CAAC,eAAe,GAAG,IAAI;gBAC7B,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC5B,MAAM,CAAC,wCAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,wCAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;YACtE,CAAC;;;YAED,GAAyB,EAAzB,CAAyB;mBAAzB,QAAQ,CAAR,yBAAyB,GAAG,CAAC;gBAC3B,GAAG,CAAC,mBAAmB;gBACvB,GAAG,CAAC,cAAc,GAAG,CAAC;gBACtB,GAAG,CAAC,UAAU,GAAG,CAAC;gBAElB,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;gBAErC,EAAE,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;wBAClB,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;wBAAb,GAAG,KAAE,SAAQ,GAAI,IAAI,CAAC,cAAc,uBAA/B,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAA2B,CAAC;4BAApC,GAAG,CAAC,IAAI,GAAR,KAAQ;4BACX,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS;4BACvC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK;wBACjC,CAAC;;wBAHI,iBAAQ;wBAAR,cAAQ;;;iCAAR,yBAAQ,IAAR,SAAQ;gCAAR,SAAQ;;;gCAAR,iBAAQ;sCAAR,cAAQ;;;;oBAIb,mBAAmB,GAAI,GAAG,GAAG,cAAc,GAAI,UAAU;gBAC3D,CAAC,MACC,mBAAmB,GAAG,GAAG;gBAG3B,MAAM,CAAC,IAAI,CAAC,IAAI,CACd,CAAqB,sBACrB,mBAAmB,EACnB,UAAU,EACV,cAAc;YAElB,CAAC;;;YAED,EAAkF,AAAlF,gFAAkF;YAClF,EAAuF,AAAvF,qFAAuF;YACvF,GAAa,EAAb,CAAa;mBAAb,QAAQ,CAAR,aAAa,CAAC,CAAC,EAAE,CAAC;gBAChB,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,CAAU,WAC9C,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;qBAE/B,MAAM,CAAE,CAAA,EACN,MAA2C,CADnC,IAAI,CAAC,OAAO,CAAC,SAAS,EAE/B,MAAA,CADC,IAAI,CAAC,OAAO,CAAC,cAAc,GAAI,CAAC,GAAI,MAAC,CAAH,CAAC,EAAC,CAAC,MAAI,CAAE;YAGjD,CAAC;;;YAED,EAAwC,AAAxC,sCAAwC;YACxC,EAAwF,AAAxF,sFAAwF;YACxF,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,IAAI,EAAE,CAAC;gBACjB,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,CAAU,WAC/C,MAAM,CAAC,IAAI,CAAC,IAAI;gBAElB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;YACrC,CAAC;;;YAED,EAAwF,AAAxF,sFAAwF;YACxF,EAAE;YACF,EAAkI,AAAlI,gIAAkI;YAClI,EAAkC,AAAlC,gCAAkC;YAClC,GAAe,EAAf,CAAe;mBAAf,QAAQ,CAAR,eAAe,GAAG,CAAC;gBACjB,GAAG,CAAC,gBAAgB,EAAE,IAAI;gBAC1B,EAAE,EAAG,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,IAC9C,MAAM,CAAC,gBAAgB;gBAGzB,GAAG,CAAC,YAAY,GAAG,CAA2B;gBAC9C,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,YAAY,IAAK,CAAG,KAAgC,MAAI,CAAlC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAC,CAAI;gBAE1D,YAAY,IAAK,CAAyB,8BACxC,MAA+D,CADrB,IAAI,CAAC,aAAa,CAAC,CAAC,GAAE,CAAE,MAEnE,MAA8C,CAD7C,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,CAAqB,uBAAG,SAAS,EAChE,CAA8C;gBAE/C,GAAG,CAAC,MAAM,GAAG,wCAAQ,CAAC,aAAa,CAAC,YAAY;gBAChD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM,OAAE,CAAC;oBACpC,IAAI,GAAG,wCAAQ,CAAC,aAAa,CAC1B,CAAc,iBAA6D,MAAmB,CAA9E,IAAI,CAAC,OAAO,CAAC,GAAG,EAAC,CAAwC,+CAAsB,MAAS,CAA7B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAC,CAAS;oBAE3G,IAAI,CAAC,WAAW,CAAC,MAAM;gBACzB,CAAC,MAAM,CAAC;oBACN,EAAoE,AAApE,kEAAoE;oBACpE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAS,UAAE,CAAqB;oBAC1D,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAQ,SAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACzD,CAAC;gBACD,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM;YACrC,CAAC;;;YAED,EAAsD,AAAtD,oDAAsD;YACtD,EAAE;YACF,EAAkC,AAAlC,gCAAkC;YAClC,GAAmB,EAAnB,CAAmB;mBAAnB,QAAQ,CAAR,mBAAmB,GAAG,CAAC;gBACrB,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAtB,WAAW,CAAa,QAAQ,EAAE,CAAC;wBAChC,yBAAM,SAAN,iBAAM,UAAN,cAAM;;wBAAX,GAAG,KAAE,SAAM,GAAI,QAAQ,qBAAlB,KAAM,IAAN,yBAAM,IAAN,KAAM,GAAN,SAAM,gBAAN,yBAAM,QAAc,CAAC;4BAArB,GAAG,CAAC,EAAE,GAAN,KAAM;4BACT,EAAE,uBAAuB,IAAI,CAAC,EAAE,CAAC,SAAS,GACxC,MAAM,CAAC,EAAE;wBAEb,CAAC;;wBAJI,iBAAM;wBAAN,cAAM;;;iCAAN,yBAAM,IAAN,SAAM;gCAAN,SAAM;;;gCAAN,iBAAM;sCAAN,cAAM;;;;gBAKb,CAAC;oBAEI,yBAAW,SAAX,iBAAW,UAAX,cAAW;;oBAAhB,GAAG,KAAE,SAAW,GAAI,CAAC;wBAAA,CAAK;wBAAE,CAAM;oBAAA,CAAC,qBAA9B,KAAW,IAAX,yBAAW,IAAX,KAAW,GAAX,SAAW,gBAAX,yBAAW,QAAqB,CAAC;wBAAjC,GAAG,CAAC,OAAO,GAAX,KAAW;wBACd,GAAG,CAAC,QAAQ;wBACZ,EAAE,EACC,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,IAEjE,MAAM,CAAC,QAAQ;oBAEnB,CAAC;;oBAPI,iBAAW;oBAAX,cAAW;;;6BAAX,yBAAW,IAAX,SAAW;4BAAX,SAAW;;;4BAAX,iBAAW;kCAAX,cAAW;;;;YAQlB,CAAC;;;YAED,EAA+C,AAA/C,6CAA+C;YAC/C,GAAmB,EAAnB,CAAmB;mBAAnB,QAAQ,CAAR,mBAAmB,GAAG,CAAC;gBACrB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAP,gBAAgB;oBACzC,MAAM,EAAL,QACP,GADa,CAAC;wBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;wBACf,GAAG,CAAE,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,MAAM,CAAE,CAAC;4BAC1C,GAAG,CAAC,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK;4BAC5C,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK;wBAEpE,CAAC;wBACD,MAAM,CAAC,MAAM;oBACf,CAAC;;YAEL,CAAC;;;YAED,EAAiD,AAAjD,+CAAiD;YACjD,GAAoB,EAApB,CAAoB;mBAApB,QAAQ,CAAR,oBAAoB,GAAG,CAAC;gBACtB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAP,gBAAgB;oBACzC,MAAM,EAAL,QACP,GADa,CAAC;wBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;wBACf,GAAG,CAAE,GAAG,CAAC,KAAK,IAAI,gBAAgB,CAAC,MAAM,CAAE,CAAC;4BAC1C,GAAG,CAAC,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK;4BAC5C,MAAM,CAAC,IAAI,CACT,gBAAgB,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK;wBAEvE,CAAC;wBACD,MAAM,CAAC,MAAM;oBACf,CAAC;;YAEL,CAAC;;;YAED,EAAqF,AAArF,mFAAqF;YACrF,GAAO,EAAP,CAAO;mBAAP,QAAQ,CAAR,OAAO,GAAG,CAAC;;gBACT,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAP,OAAO;oBACrC,MAAM,CAAN,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAc;;gBAEzC,IAAI,CAAC,oBAAoB;gBACzB,IAAI,CAAC,QAAQ,GAAG,IAAI;gBAEpB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,OAAD,YAAY,CAAC,IAAI;;YACxD,CAAC;;;YAED,GAAM,EAAN,CAAM;mBAAN,QAAQ,CAAR,MAAM,GAAG,CAAC;gBACR,MAAM,CAAC,IAAI,CAAC,QAAQ;gBACpB,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAP,OAAO;oBACrC,MAAM,CAAN,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAc;;gBAEtC,MAAM,CAAC,IAAI,CAAC,mBAAmB;YACjC,CAAC;;;YAED,EAAsC,AAAtC,oCAAsC;YACtC,GAAQ,EAAR,CAAQ;mBAAR,QAAQ,CAAR,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACd,GAAG,CAAC,YAAY,GAAG,CAAC;gBACpB,GAAG,CAAC,YAAY,GAAG,CAAG;gBAEtB,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;oBACb,GAAG,CAAC,KAAK,GAAG,CAAC;wBAAA,CAAI;wBAAE,CAAI;wBAAE,CAAI;wBAAE,CAAI;wBAAE,CAAG;oBAAA,CAAC;oBAEzC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;wBACtC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;wBAClB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE;wBAE5D,EAAE,EAAE,IAAI,IAAI,MAAM,EAAE,CAAC;4BACnB,YAAY,GAAG,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC;4BAC/D,YAAY,GAAG,IAAI;4BACnB,KAAK;wBACP,CAAC;oBACH,CAAC;oBAED,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,YAAY,IAAI,EAAE,CAAE,CAAoB,AAApB,EAAoB,AAApB,kBAAoB;gBACzE,CAAC;gBAED,MAAM,CAAE,CAAQ,UAA2B,MAA4C,CAArE,YAAY,EAAC,CAAU,aAA+C,MAAA,CAA7C,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,YAAY;YACxF,CAAC;;;YAED,EAAkE,AAAlE,gEAAkE;YAClE,GAA2B,EAA3B,CAA2B;mBAA3B,QAAQ,CAAR,2BAA2B,GAAG,CAAC;gBAC7B,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAC7B,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACvD,CAAC;oBACD,EAAE,EAAE,IAAI,CAAC,gBAAgB,GAAG,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,EAC1D,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,IAAI,CAAC,KAAK;oBAEzC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAsB;gBAC1D,CAAC,MACC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,CAAsB;YAE/D,CAAC;;;YAED,GAAI,EAAJ,CAAI;mBAAJ,QAAQ,CAAR,IAAI,CAAC,CAAC,EAAE,CAAC;gBACP,EAAE,GAAG,CAAC,CAAC,YAAY,EACjB,MAAM;gBAER,IAAI,CAAC,IAAI,CAAC,CAAM,OAAE,CAAC;gBAEnB,EAAmC,AAAnC,iCAAmC;gBACnC,EAA6B,AAA7B,2BAA6B;gBAC7B,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;gBACd,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAChD,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAGnC,EAAgE,AAAhE,8DAAgE;gBAChE,EAAE,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC;oBACjB,GAAG,CAAa,aAAc,GAAd,CAAC,CAAC,YAAY,EAAxB,KAAK,GAAK,aAAc,CAAxB,KAAK;oBACX,EAAE,EAAE,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,gBAAgB,IAAI,IAAI,EAC5D,EAA6E,AAA7E,2EAA6E;oBAC7E,IAAI,CAAC,kBAAkB,CAAC,KAAK;yBAE7B,IAAI,CAAC,WAAW,CAAC,KAAK;gBAE1B,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,CAAY,aAAE,KAAK;YAC/B,CAAC;;;YAED,GAAK,EAAL,CAAK;mBAAL,QAAQ,CAAR,KAAK,CAAC,CAAC,EAAE,CAAC;gBACR,EAAE,EACA,+BAAS,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,aAAa,GAAG,SAAS,EAAE,QAAQ,CAAP,CAAC;oBAAK,MAAM,CAAN,CAAC,CAAC,KAAK;sBAAK,IAAI,EAE1E,MAAM;gBAGR,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,CAAC;gBACpB,GAAG,CAAa,cAAe,GAAf,CAAC,CAAC,aAAa,EAAzB,KAAK,GAAK,cAAe,CAAzB,KAAK;gBAEX,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK;YAExC,CAAC;;;YAED,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,KAAK,EAAE,CAAC;oBACb,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;wBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,OAAO,CAAC,IAAI;;;oBADd,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;YAGf,CAAC;;;YAED,EAAwE,AAAxE,sEAAwE;YACxE,EAAoB,AAApB,kBAAoB;YACpB,GAAkB,EAAlB,CAAkB;mBAAlB,QAAQ,CAAR,kBAAkB,CAAC,KAAK,EAAE,CAAC;;gBACzB,MAAM,EAAE,QACZ,GADkB,CAAC;oBACb,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;wBACV,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;wBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAAW,CAAC;4BAApB,GAAG,CAAC,IAAI,GAAR,KAAQ;4BACX,GAAG,CAAC,KAAK;4BACT,EAAE,EACA,IAAI,CAAC,gBAAgB,IAAI,IAAI,KAC5B,KAAK,GAAG,IAAI,CAAC,gBAAgB,KAC9B,CAAC;gCACD,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,MAAM,CAAC,IAAI,OAAM,OAAO,CAAC,IAAI,CAAC,SAAS;qCAClC,EAAE,EAAE,KAAK,CAAC,WAAW,EAC1B,EAAgD,AAAhD,8CAAgD;gCAChD,MAAM,CAAC,IAAI,OAAM,sBAAsB,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI;qCAEzD,MAAM,CAAC,IAAI,CAAC,SAAS;4BAEzB,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC,SAAS,IAAI,IAAI;gCAC/B,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,CAAM,OAC3C,MAAM,CAAC,IAAI,OAAM,OAAO,CAAC,IAAI,CAAC,SAAS;qCAEvC,MAAM,CAAC,IAAI,CAAC,SAAS;mCAGvB,MAAM,CAAC,IAAI,CAAC,SAAS;wBAEzB,CAAC;;wBAvBI,iBAAQ;wBAAR,cAAQ;;;iCAAR,yBAAQ,IAAR,SAAQ;gCAAR,SAAQ;;;gCAAR,iBAAQ;sCAAR,cAAQ;;;;oBAwBb,MAAM,CAAC,MAAM;gBACf,CAAC;YACH,CAAC;;;YAED,EAAsE,AAAtE,oEAAsE;YACtE,GAAsB,EAAtB,CAAsB;mBAAtB,QAAQ,CAAR,sBAAsB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC;;gBACvC,GAAG,CAAC,SAAS,GAAG,SAAS,CAAC,YAAY;gBAEtC,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAP,KAAK;oBACvB,MAAM,CAAN,qCAAe,CAAC,OAAO,EAAE,CAAK,MAAE,QAAQ,CAAP,CAAC;wBAAK,MAAM,CAAN,CAAC,CAAC,GAAG,CAAC,KAAK;;;gBAEpD,GAAG,CAAC,WAAW,GAAG,QACtB,GAD4B,CAAC;;oBACvB,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAP,OAAO,EAAK,CAAC;wBACzC,EAAE,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAClB,yBAAS,SAAT,iBAAS,UAAT,cAAS;;gCAAd,GAAG,KAAE,SAAS,GAAI,OAAO,qBAApB,KAAS,IAAT,yBAAS,IAAT,KAAS,GAAT,SAAS,gBAAT,yBAAS,QAAa,CAAC;oCAAvB,GAAG,CAAC,KAAK,GAAT,KAAS;;oCACZ,EAAE,EAAE,KAAK,CAAC,MAAM,EACd,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAP,IAAI,EAAK,CAAC;wCACpB,EAAE,SACK,OAAO,CAAC,iBAAiB,IAC9B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,MAAM,CAAG,IAEjC,MAAM;wCAER,IAAI,CAAC,QAAQ,GAAI,CAAA,EAAU,MAAS,CAAjB,IAAI,EAAC,CAAC,IAAY,MAAA,CAAV,IAAI,CAAC,IAAI;wCACpC,MAAM,QAAM,OAAO,CAAC,IAAI;oCAC1B,CAAC;yCACI,EAAE,EAAE,KAAK,CAAC,WAAW,SACrB,sBAAsB,CAAC,KAAK,EAAG,CAAA,EAAU,MAAU,CAAlB,IAAI,EAAC,CAAC,IAAa,MAAA,CAAX,KAAK,CAAC,IAAI;gCAE5D,CAAC;;gCAfI,iBAAS;gCAAT,cAAS;;;yCAAT,yBAAS,IAAT,SAAS;wCAAT,SAAS;;;wCAAT,iBAAS;8CAAT,cAAS;;;;4BAiBd,EAAkE,AAAlE,gEAAkE;4BAClE,EAAyB,AAAzB,uBAAyB;4BACzB,EAAoF,AAApF,kFAAoF;4BACpF,WAAW;wBACb,CAAC;wBACD,MAAM,CAAC,IAAI;oBACb,CAAC,EAAE,YAAY;gBACjB,CAAC;gBAED,MAAM,CAAC,WAAW;YACpB,CAAC;;;YAED,EAA8D,AAA9D,4DAA8D;YAC9D,EAA6D,AAA7D,2DAA6D;YAC7D,EAA4C,AAA5C,0CAA4C;YAC5C,EAAE;YACF,EAAqE,AAArE,mEAAqE;YACrE,EAAyB,AAAzB,uBAAyB;YACzB,GAAM,EAAN,CAAM;mBAAN,QAAQ,CAAR,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;gBAClB,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,WAAW,IACxB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,GAAxB,OAAsC,EAElD,IAAI,CACF,IAAI,CAAC,OAAO,CAAC,cAAc,CACxB,OAAO,CAAC,CAAc,eAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,GAAG,EAClE,OAAO,CAAC,CAAiB,kBAAE,IAAI,CAAC,OAAO,CAAC,WAAW;qBAEnD,EAAE,GAAG,wCAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,GAC/D,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB;qBAChC,EAAE,EACP,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,IAC7B,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EACvD,CAAC;oBACD,IAAI,CACF,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,OAAO,CACvC,CAAc,eACd,IAAI,CAAC,OAAO,CAAC,QAAQ;oBAGzB,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,IAAI;gBACpC,CAAC,MACC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI;YAE7C,CAAC;;;YAED,GAAO,EAAP,CAAO;mBAAP,QAAQ,CAAR,OAAO,CAAC,IAAI,EAAE,CAAC;;gBACb,IAAI,CAAC,MAAM,GAAG,CAAC;oBACb,IAAI,EAAE,wCAAQ,CAAC,MAAM;oBACrB,QAAQ,EAAE,CAAC;oBACX,EAA+D,AAA/D,6DAA+D;oBAC/D,EAAyD,AAAzD,uDAAyD;oBACzD,KAAK,EAAE,IAAI,CAAC,IAAI;oBAChB,SAAS,EAAE,CAAC;oBACZ,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;gBAIjC,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;gBAEpB,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,KAAK;gBAE5B,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,IAAI;gBAE3B,IAAI,CAAC,iBAAiB,CAAC,IAAI;gBAE3B,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAP,KAAK,EAAK,CAAC;oBAC5B,EAAE,EAAE,KAAK,EAAE,CAAC;wBACV,IAAI,CAAC,QAAQ,GAAG,KAAK;8BAChB,gBAAgB,CAAC,CAAC;4BAAA,IAAI;wBAAA,CAAC,EAAE,KAAK,EAAG,CAA2B,AAA3B,EAA2B,AAA3B,yBAA2B;oBACnE,CAAC,MAAM,CAAC;wBACN,IAAI,CAAC,QAAQ,GAAG,IAAI;wBACpB,EAAE,QAAO,OAAO,CAAC,SAAS,QACnB,WAAW,CAAC,IAAI;wBACrB,CAA4B,AAA5B,EAA4B,AAA5B,0BAA4B;oBAChC,CAAC;0BACI,2BAA2B;gBAClC,CAAC;YACH,CAAC;;;YAED,EAA0B,AAA1B,wBAA0B;YAC1B,GAAY,EAAZ,CAAY;mBAAZ,QAAQ,CAAR,YAAY,CAAC,KAAK,EAAE,CAAC;oBACd,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;wBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,WAAW,CAAC,IAAI;;;oBADlB,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAGb,MAAM,CAAC,IAAI;YACb,CAAC;;;YAED,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,IAAI,EAAE,CAAC;gBACjB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,KAAK,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;;oBAC7D,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,MAAM;oBAC7B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,UAAU,CAAC,QAAQ;wBAAF,MAAM,OAAD,YAAY;uBAAI,CAAC,EAAG,CAAqB,AAArB,EAAqB,AAArB,mBAAqB;gBAE1E,CAAC,MACC,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAkF;YAGxF,CAAC;;;YAED,GAAiB,EAAjB,CAAiB;mBAAjB,QAAQ,CAAR,iBAAiB,CAAC,IAAI,EAAE,CAAC;gBACvB,EAAE,EACA,IAAI,CAAC,OAAO,CAAC,qBAAqB,IAClC,IAAI,CAAC,IAAI,CAAC,KAAK,eACf,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,GAAjC,OAA+C,EAC5D,CAAC;;oBACD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;oBAC9B,MAAM,CAAC,UAAU,CAAC,QAAQ;wBAAF,MAAM,OAAD,sBAAsB;uBAAI,CAAC,EAAG,CAAqB,AAArB,EAAqB,AAArB,mBAAqB;gBAClF,CAAC;YACH,CAAC;;;YAED,GAAsB,EAAtB,CAAsB;mBAAtB,QAAQ,CAAR,sBAAsB,GAAG,CAAC;;gBACxB,EAAE,EAAE,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,EAChE,MAAM;gBAGR,IAAI,CAAC,oBAAoB,GAAG,IAAI;gBAChC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK;gBACrC,MAAM,CAAC,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,EACJ,QAAQ,CAAP,OAAO,EAAK,CAAC;0BACP,IAAI,CAAC,CAAW,YAAE,IAAI,EAAE,OAAO;0BAC/B,oBAAoB,GAAG,KAAK;oBACjC,MAAM,OAAM,sBAAsB;gBACpC,CAAC;YAEL,CAAC;;;YAED,EAA6C,AAA7C,2CAA6C;YAC7C,GAAU,EAAV,CAAU;mBAAV,QAAQ,CAAR,UAAU,CAAC,IAAI,EAAE,CAAC;gBAChB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EACpC,IAAI,CAAC,YAAY,CAAC,IAAI;gBAExB,IAAI,CAAC,KAAK,GAAG,6BAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI;gBAErC,IAAI,CAAC,IAAI,CAAC,CAAa,cAAE,IAAI;gBAC7B,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAO;YAE5B,CAAC;;;YAED,EAAkE,AAAlE,gEAAkE;YAClE,GAAc,EAAd,CAAc;mBAAd,QAAQ,CAAR,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACjC,EAAsE,AAAtE,oEAAsE;gBACtE,EAAE,EAAE,iBAAiB,IAAI,IAAI,EAC3B,iBAAiB,GAAG,KAAK;oBAEtB,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,IAAI,CAAC,KAAK,CAAC,KAAK,uBAA5B,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;wBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,IAAI,iBAAiB,EACzD,IAAI,CAAC,UAAU,CAAC,IAAI;;;oBAFnB,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAKb,MAAM,CAAC,IAAI;YACb,CAAC;;;YAED,EAA+F,AAA/F,6FAA+F;YAC/F,EAAmG,AAAnG,iGAAmG;YACnG,EAAoB,AAApB,kBAAoB;YACpB,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC;;gBACxD,MAAM,CAAC,IAAI,CAAC,eAAe,CACzB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,IAAI,EACJ,QAAQ,CAAP,OAAO,EAAE,MAAM,EAAK,CAAC;oBACpB,EAAE,EAAE,MAAM,IAAI,IAAI,EAChB,EAAiC,AAAjC,+BAAiC;oBACjC,MAAM,CAAC,QAAQ,CAAC,IAAI;yBACf,CAAC;wBACN,GAAG,CAAsB,QAAY,SAAP,OAAO,EAA/B,cAAc,GAAK,QAAY,CAA/B,cAAc;wBACpB,EAAE,EAAE,cAAc,IAAI,IAAI,EACxB,cAAc,GAAG,IAAI,CAAC,IAAI;wBAE5B,GAAG,CAAC,cAAc,GAAG,MAAM,CAAC,SAAS,CACnC,cAAc,QACT,OAAO,CAAC,aAAa;wBAE5B,EAAE,EACA,cAAc,KAAK,CAAY,eAC/B,cAAc,KAAK,CAAW,YAE9B,EAAwC,AAAxC,sCAAwC;wBACxC,cAAc,GAAG,iCAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc;wBAEnE,MAAM,CAAC,QAAQ,CAAC,wCAAQ,CAAC,aAAa,CAAC,cAAc;oBACvD,CAAC;gBACH,CAAC;YAEL,CAAC;;;YAED,GAAe,EAAf,CAAe;mBAAf,QAAQ,CAAR,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC;;gBAC5E,GAAG,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU;gBAE/B,UAAU,CAAC,MAAM,GAAG,QACxB,GAD8B,CAAC;oBACzB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,MAAM;oBAEhC,EAAwE,AAAxE,sEAAwE;oBACxE,EAAE,EAAE,IAAI,CAAC,IAAI,KAAK,CAAe,gBAAE,CAAC;wBAClC,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,QAAQ,CAAC,UAAU,CAAC,MAAM;wBAE5B,MAAM;oBACR,CAAC;0BAEI,sBAAsB,CACzB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,cAAc,EACd,QAAQ;gBAEZ,CAAC;gBAED,UAAU,CAAC,aAAa,CAAC,IAAI;YAC/B,CAAC;;;YAED,EAA6C,AAA7C,2CAA6C;YAC7C,EAAE;YACF,EAAkD,AAAlD,gDAAkD;YAClD,EAAE;YACF,EAA+E,AAA/E,6EAA+E;YAC/E,EAAwE,AAAxE,sEAAwE;YACxE,GAAmB,EAAnB,CAAmB;mBAAnB,QAAQ,CAAR,mBAAmB,CACjB,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,WAAW,EACX,KAAsB,EACtB,CAAC;oBADD,eAAe,GAAf,KAAsB,cAAJ,IAAI,GAAtB,KAAsB;gBAEtB,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,QAAQ;gBAC/B,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,QAAQ;gBAE9B,EAAE,GAAG,eAAe,EAAE,CAAC;oBACrB,IAAI,CAAC,IAAI,CAAC,CAAW,YAAE,QAAQ,EAAE,QAAQ;oBACzC,EAAE,EAAE,QAAQ,EAAE,QAAQ;gBACxB,CAAC,MAAM,CAAC;;oBACN,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAP,SAAS,EAAK,CAAC;8BACtB,IAAI,CAAC,CAAW,YAAE,QAAQ,EAAE,SAAS;wBAC1C,EAAE,EAAE,QAAQ,EAAE,QAAQ;oBACxB,CAAC;oBACD,QAAQ,CAAC,OAAO,GAAG,QAAQ;oBAE3B,IAAI,CAAC,sBAAsB,CACzB,QAAQ,EACR,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,eAAe,EAC5B,IAAI,CAAC,OAAO,CAAC,cAAc,EAC3B,MAAM,EACN,WAAW;gBAEf,CAAC;YACH,CAAC;;;YAED,GAAsB,EAAtB,CAAsB;mBAAtB,QAAQ,CAAR,sBAAsB,CACpB,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY,EACZ,cAAc,EACd,QAAQ,EACR,WAAW,EACX,CAAC;;gBACD,EAAyE,AAAzE,uEAAyE;gBACzE,EAAgD,AAAhD,8CAAgD;gBAChD,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAK;gBAEtC,EAAE,EAAE,WAAW,EACb,GAAG,CAAC,WAAW,GAAG,WAAW;gBAG/B,EAA+E,AAA/E,6EAA+E;gBAC/E,cAAc,GACZ,gBAAgB,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAkB,sBAAK,CAAY,cAC/D,KAAK,GACL,cAAc;gBAEpB,GAAG,CAAC,MAAM,GAAG,QACjB,GADuB,CAAC;;oBAClB,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAP,QAAQ;wBAAK,MAAM,CAAN,QAAQ,CAAC,CAAC;;oBACvC,EAAE,EAAE,MAAM,CAAC,IAAI,KAAK,CAAW,cAAI,IAAI,KAAK,IAAI,IAAI,cAAc,EAChE,QAAQ,GAAG,QAAQ,CAAP,QAAQ;wBAClB,MAAM,CAAN,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,GAAI,CAAC;4BAC7B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAa;wBACjD,CAAC;;oBAGL,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAP,WAAW,EAAK,CAAC;wBAChC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK;wBACtB,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;wBAExB,GAAG,CAAC,UAAU,UAAQ,OAAO,CAAC,MAAM,CAAC,IAAI,SAEvC,IAAI,EACJ,KAAK,EACL,MAAM,EACN,YAAY;wBAGd,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAQ;wBAC5C,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAI;wBAEhC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,QAAQ;wBAClC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,SAAS;wBAEpC,EAAE,EAAE,WAAW,GAAG,CAAC,EAAE,CAAC;4BACpB,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC,SAAS;4BACnC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,QAAQ;wBACrC,CAAC;wBAED,MAAM,CAAE,WAAW;4BACjB,IAAI,CAAC,CAAC;gCACJ,EAAkB,AAAlB,gBAAkB;gCAClB,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gCAC7B,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;gCACf,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAmB,AAAnB,kBAAoB;gCACnB,GAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM;gCACzC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gCAClB,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAgB,AAAhB,cAAgB;gCAChB,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM;gCAC9B,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;gCACf,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAkC,AAAlC,gCAAkC;gCAClC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;gCACxB,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE;gCACf,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAmB,AAAnB,kBAAoB;gCACnB,GAAE,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;gCACxB,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK;gCAC9B,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAoC,AAApC,kCAAoC;gCACpC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE;gCACxB,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK;gCAC1C,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;gCACf,KAAK;4BACP,IAAI,CAAC,CAAC;gCACJ,EAAkB,AAAlB,iBAAkB;gCAClB,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;gCACzB,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;gCAC/B,KAAK;;wBAGT,EAAyC,AAAzC,uCAAyC;wBACzC,qCAAe,CACb,GAAG,EACH,GAAG,EACH,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,IAAI,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAC7C,UAAU,CAAC,QAAQ,EACnB,UAAU,CAAC,SAAS;wBAGtB,GAAG,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,CAAW;wBAE5C,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM;oBAErC,CAAC;gBACH,CAAC;gBAED,EAAE,EAAE,QAAQ,IAAI,IAAI,EAClB,GAAG,CAAC,OAAO,GAAG,QAAQ;gBAGxB,MAAM,CAAE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;YAChC,CAAC;;;YAED,EAA+E,AAA/E,6EAA+E;YAC/E,GAAY,EAAZ,CAAY;mBAAZ,QAAQ,CAAR,YAAY,GAAG,CAAC;gBACd,GAAG,CAAuB,QAAY,GAAZ,IAAI,CAAC,OAAO,EAAhC,eAAe,GAAK,QAAY,CAAhC,eAAe;gBACrB,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,GAAG,MAAM;gBACtD,GAAG,CAAC,CAAC,GAAG,gBAAgB;gBAExB,EAAoE,AAApE,kEAAoE;gBACpE,EAAE,EAAE,gBAAgB,IAAI,eAAe,EACrC,MAAM;gBAGR,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;gBAErC,EAAE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,GAC1B,MAAM;gBAGR,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,EAA8C,AAA9C,4CAA8C;gBAC9C,MAAM,CAAC,IAAI,CAAC,YAAY,CACtB,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,GAAG,gBAAgB;2BAGlD,CAAC,GAAG,eAAe,CAAE,CAAC;oBAC3B,EAAE,GAAG,WAAW,CAAC,MAAM,EACrB,MAAM;oBACN,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;oBAC5B,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK;oBAClC,CAAC;gBACH,CAAC;YAEL,CAAC;;;YAED,EAA6B,AAA7B,2BAA6B;YAC7B,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,IAAI,EAAE,CAAC;gBACjB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAAA,IAAI;gBAAA,CAAC;YACjC,CAAC;;;YAED,EAA+C,AAA/C,6CAA+C;YAC/C,GAAY,EAAZ,CAAY;mBAAZ,QAAQ,CAAR,YAAY,CAAC,KAAK,EAAE,CAAC;oBACd,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAAW,CAAC;wBAApB,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,UAAU,GAAG,IAAI,CAAE,CAA0B,AAA1B,EAA0B,AAA1B,wBAA0B;wBAClD,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,SAAS;wBAEhC,IAAI,CAAC,IAAI,CAAC,CAAY,aAAE,IAAI;oBAC9B,CAAC;;oBALI,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAOb,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAoB,qBAAE,KAAK;gBAGvC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK;YAC/B,CAAC;;;YAED,GAAgB,EAAhB,CAAgB;mBAAhB,QAAQ,CAAR,gBAAgB,CAAC,GAAG,EAAE,CAAC;gBACrB,GAAG,CAAC,KAAK;gBACT,MAAM,CAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CACvB,MAAM,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI,CAAC,GAAG,KAAK,GAAG;mBACjC,GAAG,CAAC,QAAQ,CAAP,IAAI;oBAAK,MAAM,CAAN,IAAI;;YACvB,CAAC;;;YAED,EAA0D,AAA1D,wDAA0D;YAC1D,EAA8C,AAA9C,4CAA8C;YAC9C,EAA+E,AAA/E,6EAA+E;YAC/E,EAAmB,AAAnB,iBAAmB;YACnB,GAAY,EAAZ,CAAY;mBAAZ,QAAQ,CAAR,YAAY,CAAC,IAAI,EAAE,CAAC;gBAClB,EAAE,EAAE,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,SAAS,EAAE,CAAC;oBACvC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG;wBAC5C,yBAAe,SAAf,iBAAe,UAAf,cAAe;;wBAApB,GAAG,KAAE,SAAe,GAAI,YAAY,qBAA/B,KAAe,IAAf,yBAAe,IAAf,KAAe,GAAf,SAAe,gBAAf,yBAAe;4BAAf,GAAG,CAAC,WAAW,GAAf,KAAe;4BAClB,WAAW,CAAC,MAAM,GAAG,wCAAQ,CAAC,QAAQ;;;wBADnC,iBAAe;wBAAf,cAAe;;;iCAAf,yBAAe,IAAf,SAAe;gCAAf,SAAe;;;gCAAf,iBAAe;sCAAf,cAAe;;;;oBAGpB,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAW,YACjC,IAAI,CAAC,GAAG,CAAC,KAAK;wBAEX,0BAAe,SAAf,kBAAe,UAAf,eAAe;;wBAApB,GAAG,KAAE,UAAe,GAAI,YAAY,qBAA/B,MAAe,IAAf,0BAAe,IAAf,MAAe,GAAf,UAAe,gBAAf,0BAAe;4BAAf,GAAG,CAAC,WAAW,GAAf,MAAe;4BAClB,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,WAAW;;;wBAD9B,kBAAe;wBAAf,eAAe;;;iCAAf,0BAAe,IAAf,UAAe;gCAAf,UAAe;;;gCAAf,kBAAe;sCAAf,eAAe;;;;oBAGpB,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,YAAY;gBAE9C,CAAC,MAAM,EAAE,EACP,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,KAAK,IAC9B,IAAI,CAAC,MAAM,KAAK,wCAAQ,CAAC,MAAM,EAC/B,CAAC;oBACD,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,QAAQ;oBAC/B,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;oBAC1B,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,CAAC;wBAAA,IAAI;oBAAA,CAAC;gBAExC,CAAC;gBAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;YAE5B,CAAC;;;YAED,GAAa,EAAb,CAAa;mBAAb,QAAQ,CAAR,aAAa,CAAC,MAAM,EAAW,CAAC;gBAAV,GAAG,CAAH,GAAO,CAAP,IAAO,GAAP,SAAO,CAAP,MAAO,EAAJ,IAAI,GAAP,GAAO,OAAP,IAAO,GAAP,CAAO,GAAP,IAAO,GAAP,CAAO,GAAP,CAAO,GAAP,IAAO,GAAP,CAAO,EAAP,IAAO,GAAP,IAAO,EAAP,IAAO,GAAP,CAAC;oBAAE,IAAI,CAAP,IAAO,GAAP,CAAO,IAAP,SAAO,CAAP,IAAO;gBAAD,CAAC;gBAC3B,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAU,WAC9B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;gBAEhC,MAAM,CAAC,MAAM;YACf,CAAC;;;YAED,GAAU,EAAV,CAAU;mBAAV,QAAQ,CAAR,UAAU,CAAC,IAAI,EAAE,CAAC;gBAChB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;oBAAA,IAAI;gBAAA,CAAC;YAChC,CAAC;;;YAED,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,KAAK,EAAE,CAAC;;gBAClB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,QAAQ,CAAP,gBAAgB,EAAK,CAAC;oBACjD,EAAE,QAAO,OAAO,CAAC,QAAQ,EAAE,CAAC;wBAC1B,EAAsE,AAAtE,oEAAsE;wBACtE,EAAkC,AAAlC,gCAAkC;wBAClC,GAAG,CAAC,eAAe,GAAG,gBAAgB,CAAC,CAAC;wBACxC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,SAChB,OAAO,CAAC,QAAQ,WACf,OAAO,CAAC,aAAa,IACzB,eAAe,CAAC,IAAI,SAAQ,OAAO,CAAC,SAAS;wBACjD,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,eAAe,GAAG,IAAI,CAAC,IAAI,CACzC,eAAe,CAAC,IAAI,SAAQ,OAAO,CAAC,SAAS;oBAEjD,CAAC;oBAED,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;;wBAC5B,EAAsC,AAAtC,oCAAsC;wBAEtC,EAAwF,AAAxF,sFAAwF;wBACxF,EAAkD,AAAlD,gDAAkD;wBAClD,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;wBAClB,GAAG,CAAC,eAAe,GAAG,gBAAgB,CAAC,CAAC;wBACxC,GAAG,CAAC,iBAAiB,GAAG,CAAC;wBAEzB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;wBAEvB,GAAG,CAAC,eAAe,GAAG,QAC9B,GADoC,CAAC;4BAC3B,GAAG,CAAC,UAAU,GAAG,CAAC;4BAElB,EAAoE,AAApE,kEAAoE;kCAC7D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,MAAM,SAAS,CACjD,UAAU;4BAGZ,EAAyD,AAAzD,uDAAyD;4BACzD,EAAE,EAAE,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,MAAM;4BAErD,iBAAiB;4BAEjB,GAAG,CAAC,KAAK,GAAG,UAAU,UAAQ,OAAO,CAAC,SAAS;4BAC/C,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAChB,KAAK,UAAQ,OAAO,CAAC,SAAS,EAC9B,eAAe,CAAC,IAAI;4BAGtB,GAAG,CAAC,SAAS,GAAG,CAAC;gCACf,IAAI,SAAO,aAAa,CAAC,CAAC;gCAC1B,IAAI,EAAE,eAAe,CAAC,WAAW,GAC7B,eAAe,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,IACtC,eAAe,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG;gCACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gCAC9B,UAAU,EAAE,UAAU;4BACxB,CAAC;4BAED,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC;gCAChC,IAAI,EAAE,IAAI;gCACV,KAAK,EAAE,UAAU;gCACjB,SAAS,EAAE,SAAS;gCACpB,MAAM,EAAE,wCAAQ,CAAC,SAAS;gCAC1B,QAAQ,EAAE,CAAC;gCACX,OAAO,EAAE,CAAC;4BACZ,CAAC;mCAEI,WAAW,CAAC,KAAK,EAAE,CAAC;gCAAA,SAAS;4BAAA,CAAC;wBACrC,CAAC;wBAED,IAAI,CAAC,MAAM,CAAC,mBAAmB,GAAG,QAAQ,CAAP,KAAK,EAAE,QAAQ,EAAK,CAAC;;4BACtD,GAAG,CAAC,WAAW,GAAG,IAAI;4BACtB,KAAK,CAAC,MAAM,GAAG,wCAAQ,CAAC,OAAO;4BAE/B,EAAgC,AAAhC,8BAAgC;4BAChC,KAAK,CAAC,SAAS,GAAG,IAAI;4BACtB,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,YAAY;4BACvC,KAAK,CAAC,eAAe,GAAG,KAAK,CAAC,GAAG,CAAC,qBAAqB;4BACvD,EAAyD,AAAzD,uDAAyD;4BACzD,KAAK,CAAC,GAAG,GAAG,IAAI;4BAEhB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAAI,CAAC;gCACrD,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,SAAS,EACrC,MAAM,CAAC,eAAe;gCAExB,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,OAAO,EACnD,WAAW,GAAG,KAAK;4BAEvB,CAAC;4BAED,EAAE,EAAE,WAAW,SACR,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,QAC9C,GADoD,CAAC;uCAClC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI;4BACtC,CAAC;wBAEL,CAAC;wBAED,EAAE,QAAO,OAAO,CAAC,oBAAoB,EACnC,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAChD,eAAe;6BAGjB,eAAe;oBAEnB,CAAC,MAAM,CAAC;wBACN,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC;wBACnB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GACjC,UAAU,CAAC,CAAC,IAAI,CAAC;4BACf,IAAI,QAAO,aAAa,CAAC,CAAC;4BAC1B,IAAI,EAAE,gBAAgB,CAAC,CAAC;4BACxB,QAAQ,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,QAAQ;wBACpC,CAAC;8BAEE,WAAW,CAAC,KAAK,EAAE,UAAU;oBACpC,CAAC;gBACH,CAAC;YACH,CAAC;;;YAED,EAAkD,AAAlD,gDAAkD;YAClD,GAAS,EAAT,CAAS;mBAAT,QAAQ,CAAR,SAAS,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;gBACpB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAAI,CAAC;oBACrD,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,SAAS,IACnC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,KAAK,GAAG,EAEjC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAE/B,CAAC;YACH,CAAC;;;YAED,EAA4D,AAA5D,0DAA4D;YAC5D,EAAE;YACF,EAA8E,AAA9E,4EAA8E;YAC9E,EAA6E,AAA7E,2EAA6E;YAC7E,EAAmD,AAAnD,iDAAmD;YACnD,GAAW,EAAX,CAAW;mBAAX,QAAQ,CAAR,WAAW,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC;;gBAC9B,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,cAAc;oBAGvB,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBADb,EAA2E,AAA3E,yEAA2E;oBAC3E,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;wBAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,GAAG,GAAG,GAAG;;;oBADX,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAGb,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EACzB,EAAwE,AAAxE,sEAAwE;gBACxE,EAAmC,AAAnC,iCAAmC;gBACnC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,EAAE,UAAU,EAAE,GAAG,GAAG,GAAG;gBAG5D,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU;gBACtE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU;gBAChE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI;gBAE1B,EAAkG,AAAlG,gGAAkG;gBAClG,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK;gBAC5D,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK;gBAEzE,EAA6E,AAA7E,2EAA6E;gBAC7E,GAAG,CAAC,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,eAAe;gBAEpD,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAP,CAAC,EAAK,CAAC;0BACd,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;gBACvC,CAAC;gBAED,GAAG,CAAC,SAAS,GAAG,QACpB,GAD0B,CAAC;2BAChB,kBAAkB,CACrB,KAAK,EACL,GAAG,EACF,CAAuB,yBAA8B,MAAQ,QAA/B,OAAO,CAAC,OAAO,GAAG,IAAI,EAAC,CAAQ;gBAElE,CAAC;gBAED,GAAG,CAAC,OAAO,GAAG,QAClB,GADwB,CAAC;2BACd,kBAAkB,CAAC,KAAK,EAAE,GAAG;gBACpC,CAAC;gBAED,EAAiD,AAAjD,+CAAiD;gBACjD,GAAG,CAAC,WAAW,GAAG,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG;gBACvD,WAAW,CAAC,UAAU,GAAG,QAC5B,CAD6B,CAAC;oBACzB,MAAM,QAAD,0BAA0B,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;;gBAE/C,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,GACrC,CAAC;oBACC,MAAM,EAAE,CAAkB;oBAC1B,CAAe,gBAAE,CAAU;oBAC3B,CAAkB,mBAAE,CAAgB;gBACtC,CAAC,GACD,CAAC;gBAAA,CAAC;gBAEN,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EACzB,OAAO,CAAC,CAAc,iBAAI,KAAK,CAAC,CAAC,EAAE,IAAI;gBAGzC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,yCAAM,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;gBAGtC,GAAG,CAAE,GAAG,CAAC,UAAU,IAAI,OAAO,CAAE,CAAC;oBAC/B,GAAG,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU;oBACpC,EAAE,EAAE,WAAW,EACb,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW;gBAEhD,CAAC;gBAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;wBAGvB,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;wBAFb,EAAqE,AAArE,mEAAqE;wBACrE,EAA2C,AAA3C,yCAA2C;wBAC3C,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;4BAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;4BACX,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,GAAG;;;wBAD3B,iBAAQ;wBAAR,cAAQ;;;iCAAR,yBAAQ,IAAR,SAAQ;gCAAR,SAAQ;;;gCAAR,iBAAQ;sCAAR,cAAQ;;;;oBAGb,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,GAAG;oBAEzC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK;gBACrC,CAAC,MAAM,CAAC;oBACN,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ;oBAE3B,EAAiC,AAAjC,+BAAiC;oBACjC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;wBACxB,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM;wBAC1C,EAAE,EAAE,MAAM,CAAC,gBAAgB,KAAK,CAAU,WACxC,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CACtC,IAAI,EACJ,KAAK,EACL,GAAG,EACH,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI;wBAIlE,GAAG,CAAE,GAAG,CAAC,GAAG,IAAI,gBAAgB,CAAE,CAAC;4BACjC,GAAG,CAAC,KAAK,GAAG,gBAAgB,CAAC,GAAG;4BAChC,EAAE,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,GACrB,EAA8C,AAA9C,4CAA8C;4BAC9C,EAA+C,AAA/C,6CAA+C;4BAC/C,EAAgB,AAAhB,cAAgB;4BAChB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GACjC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iCAG9B,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK;wBAE9B,CAAC;oBACH,CAAC;wBAGI,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;wBADb,EAAgD,AAAhD,8CAAgD;wBAChD,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ;4BAAR,GAAG,CAAC,IAAI,GAAR,KAAQ;4BACX,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,GAAG,EAAE,QAAQ;;;wBADrC,iBAAQ;wBAAR,cAAQ;;;iCAAR,yBAAQ,IAAR,SAAQ;gCAAR,SAAQ;;;gCAAR,iBAAQ;sCAAR,cAAQ;;;;oBAGb,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAC7B,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,GAAG,EAAE,QAAQ;oBAGnD,IAAI,CAAC,mBAAmB,CAAC,QAAQ;oBAEjC,EAAwB,AAAxB,sBAAwB;oBACxB,EAAwF,AAAxF,sFAAwF;oBACxF,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAI,CAAC;wBAC3C,GAAG,CAAC,SAAS,GAAG,UAAU,CAAC,CAAC;wBAC5B,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,QAAQ;oBACpE,CAAC;oBAED,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK;gBACzC,CAAC;YACH,CAAC;;;YAED,EAA8G,AAA9G,4GAA8G;YAC9G,GAAe,EAAf,CAAe;mBAAf,QAAQ,CAAR,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;;oBAK1B,KAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAI,EAAE,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAP,eAAe,EAAK,CAAC;wBACpE,gBAAgB,CAAC,CAAC,IAAI,eAAe;wBACrC,EAAE,IAAI,WAAW,KAAK,KAAK,CAAC,MAAM,EAChC,IAAI,CAAC,gBAAgB;oBAEzB,CAAC;;gBATH,GAAG,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBACzB,EAAyF,AAAzF,uFAAyF;gBACzF,GAAG,CAAC,WAAW,GAAG,CAAC;gBACnB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;YAQrC,CAAC;;;YAED,EAA4E,AAA5E,0EAA4E;YAC5E,GAAmB,EAAnB,CAAmB;mBAAnB,QAAQ,CAAR,mBAAmB,CAAC,QAAQ,EAAE,CAAC;oBAGtB,yBAAS,SAAT,iBAAS,UAAT,cAAS;gBAFhB,EAAoC,AAApC,kCAAoC;gBACpC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,CAAM;oBACjC,GAAG,KAAE,SAAS,GAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC7C,CAAiC,sDAD9B,KAAS,IAAT,yBAAS,IAAT,KAAS,GAAT,SAAS,gBAAT,yBAAS,QAEX,CAAC;wBAFC,GAAG,CAAC,KAAK,GAAT,KAAS;wBAGZ,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,CAAM;wBACzC,GAAG,CAAC,SAAS,GAAG,KAAK,CAAC,YAAY,CAAC,CAAM;wBACzC,EAAE,EAAE,SAAS,EAAE,SAAS,GAAG,SAAS,CAAC,WAAW;wBAEhD,EAAqD,AAArD,mDAAqD;wBACrD,EAAE,EAAE,MAAM,CAAC,SAAS,KAAK,CAAW,cAAI,SAAS,KAAK,IAAI,EAAE,QAAQ;wBAEpE,EAAE,EAAE,KAAK,CAAC,OAAO,KAAK,CAAQ,WAAI,KAAK,CAAC,YAAY,CAAC,CAAU,YAAG,CAAC;gCAE5D,yBAAU,SAAV,iBAAU,UAAV,cAAU;;gCADf,EAA2B,AAA3B,yBAA2B;gCAC3B,GAAG,KAAE,SAAU,GAAI,KAAK,CAAC,OAAO,qBAA3B,KAAU,IAAV,yBAAU,IAAV,KAAU,GAAV,SAAU,gBAAV,yBAAU;oCAAV,GAAG,CAAC,MAAM,GAAV,KAAU;oCACb,EAAE,EAAE,MAAM,CAAC,QAAQ,EACjB,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK;;;gCAFtC,iBAAU;gCAAV,cAAU;;;yCAAV,yBAAU,IAAV,SAAU;wCAAV,SAAU;;;wCAAV,iBAAU;8CAAV,cAAU;;;;wBAKjB,CAAC,MAAM,EAAE,GACN,SAAS,IACT,SAAS,KAAK,CAAU,aAAI,SAAS,KAAK,CAAO,UAClD,KAAK,CAAC,OAAO,EAEb,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK;oBAE1C,CAAC;;oBAxBI,iBAAS;oBAAT,cAAS;;;6BAAT,yBAAS,IAAT,SAAS;4BAAT,SAAS;;;4BAAT,iBAAS;kCAAT,cAAS;;;;YA0BlB,CAAC;;;YAED,EAAoE,AAApE,kEAAoE;YACpE,EAAmE,AAAnE,iEAAmE;YACnE,GAA0B,EAA1B,CAA0B;mBAA1B,QAAQ,CAAR,0BAA0B,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;oBAGlC,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;gBAFf,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO;oBAC1B,EAAuC,AAAvC,qCAAuC;oBACvC,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAAW,CAAC;wBAApB,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,KAAK,IACjB,IAAI,CAAC,MAAM,CAAC,SAAS,IACrB,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAK1C,QAAQ;wBAGV,EAAE,EAAE,CAAC,EAAE,CAAC;4BACN,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAI,GAAG,GAAG,CAAC,CAAC,MAAM,GAAI,CAAC,CAAC,KAAK;4BACjD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;4BAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;wBAClC,CAAC,MAAM,CAAC;4BACN,EAA6B,AAA7B,2BAA6B;4BAC7B,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,GAAG;4BAC1B,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK;wBAC3C,CAAC;wBAED,IAAI,CAAC,IAAI,CACP,CAAgB,iBAChB,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS;oBAEzB,CAAC;;oBA5BI,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;qBA6BR,CAAC;oBACN,EAA8B,AAA9B,4BAA8B;oBAE9B,EAAwE,AAAxE,sEAAwE;oBACxE,EAA6C,AAA7C,2CAA6C;oBAC7C,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;oBAElB,EAA0E,AAA1E,wEAA0E;oBAC1E,EAAY,AAAZ,UAAY;oBACZ,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG;oBAEpC,EAAE,EAAE,CAAC,EAAE,CAAC;wBACN,KAAK,CAAC,QAAQ,GAAI,GAAG,GAAG,CAAC,CAAC,MAAM,GAAI,CAAC,CAAC,KAAK;wBAC3C,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK;wBACrB,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;oBAC5B,CAAC,MAAM,CAAC;wBACN,EAA6B,AAA7B,2BAA6B;wBAC7B,KAAK,CAAC,QAAQ,GAAG,GAAG;wBACpB,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;oBAC/B,CAAC;oBAED,EAAkE,AAAlE,gEAAkE;oBAClE,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAC;oBACxB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC;oBACrB,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC;oBACzB,GAAG,CAAE,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,GAChD,EAAE,EACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KACpB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAW,YACrD,CAAC;wBACD,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ;wBACtD,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK;wBAChD,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,SAAS;oBAC1D,CAAC;oBAEH,EAAwE,AAAxE,sEAAwE;oBACxE,EAAqB,AAArB,mBAAqB;oBACrB,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe;oBAEzE,IAAI,CAAC,IAAI,CACP,CAAgB,iBAChB,IAAI,EACJ,IAAI,CAAC,MAAM,CAAC,QAAQ,EACpB,IAAI,CAAC,MAAM,CAAC,SAAS;gBAEzB,CAAC;YACH,CAAC;;;YAED,GAAkB,EAAlB,CAAkB;mBAAlB,QAAQ,CAAR,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;gBACjC,GAAG,CAAC,QAAQ;gBAEZ,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,QAAQ,EACvC,MAAM;gBAGR,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,CAAC,EACtB,MAAM;gBAGR,EAAE,EAAE,GAAG,CAAC,YAAY,KAAK,CAAa,gBAAI,GAAG,CAAC,YAAY,KAAK,CAAM,OAAE,CAAC;oBACtE,QAAQ,GAAG,GAAG,CAAC,YAAY;oBAE3B,EAAE,EACA,GAAG,CAAC,iBAAiB,CAAC,CAAc,mBACnC,GAAG,CAAC,iBAAiB,CAAC,CAAc,eAAE,OAAO,CAAC,CAAkB,oBAEjE,GAAG,CAAC,CAAC;wBACH,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ;oBAChC,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;wBACf,CAAC,GAAG,KAAK;wBACT,QAAQ,GAAG,CAAoC;oBACjD,CAAC;gBAEL,CAAC;gBAED,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,GAAG;gBAE1C,EAAE,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,GACzC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ;qBAE5C,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EACzB,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CACjC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAC5B,QAAQ;qBAGV,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;YAGvC,CAAC;;;YAED,GAAkB,EAAlB,CAAkB;mBAAlB,QAAQ,CAAR,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;gBACxC,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,KAAK,wCAAQ,CAAC,QAAQ,EACvC,MAAM;gBAGR,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;oBACxD,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;oBACxC,EAAE,GAAE,KAAK,CAAC,OAAO,MAAK,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;wBACpD,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;4BAAA,KAAK,CAAC,SAAS;wBAAA,CAAC;wBACzC,MAAM;oBACR,CAAC,MACC,OAAO,CAAC,IAAI,CAAC,CAA0C;gBAE3D,CAAC;gBAED,IAAI,CAAC,gBAAgB,CACnB,KAAK,EACL,QAAQ,IACN,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAgB,iBAAE,GAAG,CAAC,MAAM,GACrE,GAAG;YAEP,CAAC;;;YAED,GAAa,EAAb,CAAa;mBAAb,QAAQ,CAAR,aAAa,CAAC,GAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;gBACnC,EAAE,EAAE,GAAG,CAAC,UAAU,IAAI,CAAC,EAAE,CAAC;oBACxB,OAAO,CAAC,IAAI,CACV,CAA+E;oBAEjF,MAAM;gBACR,CAAC;gBACD,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;oBACzB,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;wBAC5B,GAAK,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG;wBAC1C,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI;oBAC/B,CAAC,MACC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;uBAGlB,GAAG,CAAC,IAAI,CAAC,QAAQ;YAErB,CAAC;;;YAED,EAAiD,AAAjD,+CAAiD;YACjD,EAAsE,AAAtE,oEAAsE;YACtE,GAAS,EAAT,CAAS;mBAAT,QAAQ,CAAR,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;oBAC5B,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAAW,CAAC;wBAApB,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,OAAO;wBAC9B,IAAI,CAAC,IAAI,CAAC,CAAS,UAAE,IAAI,EAAE,YAAY,EAAE,CAAC;wBAC1C,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;oBAC5B,CAAC;;oBAJI,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAKb,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;oBAChC,IAAI,CAAC,IAAI,CAAC,CAAiB,kBAAE,KAAK,EAAE,YAAY,EAAE,CAAC;oBACnD,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,KAAK;gBACrC,CAAC;gBAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;YAE5B,CAAC;;;YAED,EAAiD,AAAjD,+CAAiD;YACjD,EAAsE,AAAtE,oEAAsE;YACtE,GAAgB,EAAhB,CAAgB;mBAAhB,QAAQ,CAAR,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC;oBAChC,yBAAQ,SAAR,iBAAQ,UAAR,cAAQ;;oBAAb,GAAG,KAAE,SAAQ,GAAI,KAAK,qBAAjB,KAAQ,IAAR,yBAAQ,IAAR,KAAQ,GAAR,SAAQ,gBAAR,yBAAQ,QAAW,CAAC;wBAApB,GAAG,CAAC,IAAI,GAAR,KAAQ;wBACX,IAAI,CAAC,MAAM,GAAG,wCAAQ,CAAC,KAAK;wBAC5B,IAAI,CAAC,IAAI,CAAC,CAAO,QAAE,IAAI,EAAE,OAAO,EAAE,GAAG;wBACrC,IAAI,CAAC,IAAI,CAAC,CAAU,WAAE,IAAI;oBAC5B,CAAC;;oBAJI,iBAAQ;oBAAR,cAAQ;;;6BAAR,yBAAQ,IAAR,SAAQ;4BAAR,SAAQ;;;4BAAR,iBAAQ;kCAAR,cAAQ;;;;gBAKb,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;oBAChC,IAAI,CAAC,IAAI,CAAC,CAAe,gBAAE,KAAK,EAAE,OAAO,EAAE,GAAG;oBAC9C,IAAI,CAAC,IAAI,CAAC,CAAkB,mBAAE,KAAK;gBACrC,CAAC;gBAED,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAC/B,MAAM,CAAC,IAAI,CAAC,YAAY;YAE5B,CAAC;;;;YAjrDM,GAAS,EAAT,CAAS;mBAAhB,QAAQ,CAAD,SAAS,GAAG,CAAC;gBAClB,EAA+C,AAA/C,6CAA+C;gBAC/C,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,wCAAO;gBAEhC,EAOG,AAPH;;;;;;;KAOG,AAPH,EAOG,CACH,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;oBACvB,CAAM;oBACN,CAAW;oBACX,CAAS;oBACT,CAAW;oBACX,CAAU;oBACV,CAAW;oBACX,CAAW;oBACX,CAAY;oBACZ,CAAa;oBACb,CAAW;oBACX,CAAO;oBACP,CAAe;oBACf,CAAY;oBACZ,CAAoB;oBACpB,CAAgB;oBAChB,CAAqB;oBACrB,CAAS;oBACT,CAAiB;oBACjB,CAAS;oBACT,CAAiB;oBACjB,CAAU;oBACV,CAAkB;oBAClB,CAAU;oBACV,CAAkB;oBAClB,CAAO;oBACP,CAAkB;oBAClB,CAAiB;oBACjB,CAAe;gBACjB,CAAC;gBAED,IAAI,CAAC,SAAS,CAAC,eAAe,GAAG,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,oBAAoB,GAAG,KAAK;YAC7C,CAAC;;;YAsoDM,GAAM,EAAN,CAAM;mBAAb,QAAQ,CAAD,MAAM,GAAG,CAAC;gBACf,MAAM,CAAC,CAAsC,sCAAC,OAAO,UAEnD,QAAQ,CAAE,CAAC,EAAE,CAAC;oBACZ,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC,MAAM,KAAK,EAAE,GAAI,CAAC,EAC9B,CAAC,GAAG,CAAC,KAAK,CAAG,KAAG,CAAC,GAAI,CAAC,GAAG,CAAG,GAAI,CAAG;oBACrC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACtB,CAAC;YAEL,CAAC;;;WA7rDkB,wCAAQ;EAAS,wCAAO;AA+rD7C,wCAAQ,CAAC,SAAS;AAElB,EAA4E,AAA5E,0EAA4E;AAC5E,EAAsD,AAAtD,oDAAsD;AACtD,EAAE;AACF,EAAW,AAAX,SAAW;AACX,EAAE;AACF,EAAiE,AAAjE,+DAAiE;AACjE,EAAE;AACF,EAAe,AAAf,aAAe;AACf,EAAE;AACF,EAAkF,AAAlF,gFAAkF;AAClF,wCAAQ,CAAC,OAAO,GAAG,CAAC;AAAA,CAAC;AAErB,EAAqE,AAArE,mEAAqE;AACrE,wCAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IAC/C,EAAqE,AAArE,mEAAqE;IACrE,EAAE,EAAE,OAAO,CAAC,YAAY,CAAC,CAAI,MAC3B,MAAM,CAAC,wCAAQ,CAAC,OAAO,CAAC,8BAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,CAAI;SAE1D,MAAM,CAAC,SAAS;AAEpB,CAAC;AAED,EAAyC,AAAzC,uCAAyC;AACzC,wCAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;AAEvB,EAAgD,AAAhD,8CAAgD;AAChD,wCAAQ,CAAC,UAAU,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IACxC,EAAE,EAAE,MAAM,CAAC,OAAO,KAAK,CAAQ,SAC7B,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO;IAE1C,EAAE,GAAG,OAAO,IAAI,IAAI,GAAG,OAAO,CAAC,QAAQ,GAAG,SAAS,KAAK,IAAI,EAC1D,KAAK,CAAC,GAAG,CAAC,KAAK,CACb,CAAgN;IAGpN,MAAM,CAAC,OAAO,CAAC,QAAQ;AACzB,CAAC;AAED,EAAmE,AAAnE,iEAAmE;AACnE,wCAAQ,CAAC,QAAQ,GAAG,QAAQ,GAAI,CAAC;IAC/B,GAAG,CAAC,SAAS;IACb,EAAE,EAAE,QAAQ,CAAC,gBAAgB,EAC3B,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,CAAW;SAC5C,CAAC;QACN,SAAS,GAAG,CAAC,CAAC;QACd,EAAQ,AAAR,MAAQ;QACR,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAP,QAAQ;YAC3B,MAAM,EAAL,QACP,GADa,CAAC;gBACN,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;oBACV,yBAAM,SAAN,iBAAM,UAAN,cAAM;;oBAAX,GAAG,KAAE,SAAM,GAAI,QAAQ,qBAAlB,KAAM,IAAN,yBAAM,IAAN,KAAM,GAAN,SAAM,gBAAN,yBAAM;wBAAN,GAAG,CAAC,EAAE,GAAN,KAAM;wBACT,EAAE,uBAAuB,IAAI,CAAC,EAAE,CAAC,SAAS,GACxC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;6BAE7B,MAAM,CAAC,IAAI,CAAC,SAAS;;;oBAJpB,iBAAM;oBAAN,cAAM;;;6BAAN,yBAAM,IAAN,SAAM;4BAAN,SAAM;;;4BAAN,iBAAM;kCAAN,cAAM;;;;gBAOX,MAAM,CAAC,MAAM;YACf,CAAC;;QACH,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAK;QACjD,aAAa,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAM;IACpD,CAAC;IAED,MAAM,EAAE,QACV,GADgB,CAAC;QACb,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;YACV,yBAAY,SAAZ,iBAAY,UAAZ,cAAY;;YAAjB,GAAG,KAAE,SAAY,GAAI,SAAS,qBAAzB,KAAY,IAAZ,yBAAY,IAAZ,KAAY,GAAZ,SAAY,gBAAZ,yBAAY;gBAAZ,GAAG,CAAC,QAAQ,GAAZ,KAAY;gBACf,EAAgF,AAAhF,8EAAgF;gBAChF,EAAE,EAAE,wCAAQ,CAAC,iBAAiB,CAAC,QAAQ,MAAM,KAAK,EAChD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,wCAAQ,CAAC,QAAQ;qBAEjC,MAAM,CAAC,IAAI,CAAC,SAAS;;;YALpB,iBAAY;YAAZ,cAAY;;;qBAAZ,yBAAY,IAAZ,SAAY;oBAAZ,SAAY;;;oBAAZ,iBAAY;0BAAZ,cAAY;;;;QAQjB,MAAM,CAAC,MAAM;IACf,CAAC;AACH,CAAC;AAED,EAAwE,AAAxE,sEAAwE;AACxE,EAAE;AACF,EAA8E,AAA9E,4EAA8E;AAC9E,EAAyE,AAAzE,uEAAyE;AACzE,EAAiB,AAAjB,eAAiB;AACjB,EAAE;AACF,EAA2E,AAA3E,yEAA2E;AAC3E,EAAE;AACF,EAAsE,AAAtE,oEAAsE;AACtE,EAAiB,AAAjB,eAAiB;AACjB,wCAAQ,CAAC,eAAe,GAAG,CAAC;IAC1B,EAA0G,AAA1G,wGAA0G;;AAE5G,CAAC;AAED,EAAqC,AAArC,mCAAqC;AACrC,wCAAQ,CAAC,kBAAkB,GAAG,QAAQ,GAAI,CAAC;IACzC,GAAG,CAAC,cAAc,GAAG,IAAI;IAEzB,EAAE,EACA,MAAM,CAAC,IAAI,IACX,MAAM,CAAC,UAAU,IACjB,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC,IAAI,IACX,MAAM,CAAC,QAAQ,IACf,QAAQ,CAAC,aAAa;QAEtB,EAAE,IAAI,CAAW,cAAI,QAAQ,CAAC,aAAa,CAAC,CAAG,MAC7C,cAAc,GAAG,KAAK;aACjB,CAAC;YACN,EAAE,EAAE,wCAAQ,CAAC,mBAAmB,KAAK,SAAS,EAC5C,EAAoE,AAApE,kEAAoE;YACpE,EAAiB,AAAjB,eAAiB;YACjB,wCAAQ,CAAC,eAAe,GAAG,wCAAQ,CAAC,mBAAmB;gBAGpD,yBAAS,SAAT,iBAAS,UAAT,cAAS;;gBADd,EAAoD,AAApD,kDAAoD;gBACpD,GAAG,KAAE,SAAS,GAAI,wCAAQ,CAAC,eAAe,qBAArC,KAAS,IAAT,yBAAS,IAAT,KAAS,GAAT,SAAS,gBAAT,yBAAS;oBAAT,GAAG,CAAC,KAAK,GAAT,KAAS;oBACZ,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC;wBACpC,cAAc,GAAG,KAAK;wBACtB,QAAQ;oBACV,CAAC;;;gBAJE,iBAAS;gBAAT,cAAS;;;yBAAT,yBAAS,IAAT,SAAS;wBAAT,SAAS;;;wBAAT,iBAAS;8BAAT,cAAS;;;;QAMhB,CAAC;WAED,cAAc,GAAG,KAAK;IAGxB,MAAM,CAAC,cAAc;AACvB,CAAC;AAED,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IAC3C,EAAqD,AAArD,mDAAqD;IACrD,EAAsF,AAAtF,oFAAsF;IACtF,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAG,IAAE,CAAC;IAE1C,EAAkC,AAAlC,gCAAkC;IAClC,GAAG,CAAC,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAG,IAAE,CAAC,EAAE,KAAK,CAAC,CAAG,IAAE,CAAC,EAAE,KAAK,CAAC,CAAG,IAAE,CAAC;IAEjE,EAAkD,AAAlD,gDAAkD;IAClD,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM;IAC1C,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE;IAC1B,GAAG,CACD,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,GAAG,EAClD,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,GAAG,EACzB,GAAG,GAAG,CAAC,KAAK,CAAC,GAEb,EAAE,CAAC,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;IAGjC,EAAkC,AAAlC,gCAAkC;IAClC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAAA,EAAE;IAAA,CAAC,EAAE,CAAC;QAAC,IAAI,EAAE,UAAU;IAAC,CAAC;AAC5C,CAAC;AAED,EAA6C,AAA7C,2CAA6C;AAC7C,GAAK,CAAC,6BAAO,GAAG,QAAQ,CAAP,IAAI,EAAE,YAAY;IACjC,MAAM,CAAN,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAP,IAAI;QAAK,MAAM,CAAN,IAAI,KAAK,YAAY;OAAE,GAAG,CAAC,QAAQ,CAAP,IAAI;QAAK,MAAM,CAAN,IAAI;;;AAEjE,EAA2B,AAA3B,yBAA2B;AAC3B,GAAK,CAAC,8BAAQ,GAAG,QAAQ,CAAP,GAAG;IACnB,MAAM,CAAN,GAAG,CAAC,OAAO,eAAe,QAAQ,CAAP,KAAK;QAAK,MAAM,CAAN,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,WAAW;;;AAElE,EAAiC,AAAjC,+BAAiC;AACjC,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,MAAM,EAAE,CAAC;IAC1C,GAAG,CAAC,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAK;IACtC,GAAG,CAAC,SAAS,GAAG,MAAM;IACtB,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACzB,CAAC;AAED,EAAgE,AAAhE,8DAAgE;AAChE,wCAAQ,CAAC,aAAa,GAAG,QAAQ,CAAE,OAAO,EAAE,SAAS,EAAE,CAAC;IACtD,EAAE,EAAE,OAAO,KAAK,SAAS,EACvB,MAAM,CAAC,IAAI;IACX,CAA8C,AAA9C,EAA8C,AAA9C,4CAA8C;UACxC,OAAO,GAAG,OAAO,CAAC,UAAU,CAAG,CAAC;QACtC,EAAE,EAAE,OAAO,KAAK,SAAS,EACvB,MAAM,CAAC,IAAI;IAEf,CAAC;IACD,MAAM,CAAC,KAAK;AACd,CAAC;AAED,wCAAQ,CAAC,UAAU,GAAG,QAAQ,CAAE,EAAE,EAAE,IAAI,EAAE,CAAC;IACzC,GAAG,CAAC,OAAO;IACX,EAAE,EAAE,MAAM,CAAC,EAAE,KAAK,CAAQ,SACxB,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE;SAC9B,EAAE,EAAE,EAAE,CAAC,QAAQ,IAAI,IAAI,EAC5B,OAAO,GAAG,EAAE;IAEd,EAAE,EAAE,OAAO,IAAI,IAAI,EACjB,KAAK,CAAC,GAAG,CAAC,KAAK,CACZ,CAAU,WAAO,MAA0E,CAA/E,IAAI,EAAC,CAA0E;IAGhG,MAAM,CAAC,OAAO;AAChB,CAAC;AAED,wCAAQ,CAAC,WAAW,GAAG,QAAQ,CAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IAC3C,GAAG,CAAC,EAAE,EAAE,QAAQ;IAChB,EAAE,EAAE,GAAG,YAAY,KAAK,EAAE,CAAC;QACzB,QAAQ,GAAG,CAAC,CAAC;QACb,GAAG,CAAC,CAAC;gBACE,yBAAE,SAAF,iBAAE,UAAF,cAAE;;gBAAP,GAAG,KAAE,SAAE,GAAI,GAAG,qBAAT,KAAE,IAAF,yBAAE,IAAF,KAAE,GAAF,SAAE,gBAAF,yBAAE;oBAAF,EAAE,GAAF,KAAE;oBACL,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,IAAI;;;gBADnC,iBAAE;gBAAF,cAAE;;;yBAAF,yBAAE,IAAF,SAAE;wBAAF,SAAE;;;wBAAF,iBAAE;8BAAF,cAAE;;;;QAGT,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACX,QAAQ,GAAG,IAAI;QACjB,CAAC;IACH,CAAC,MAAM,EAAE,EAAE,MAAM,CAAC,GAAG,KAAK,CAAQ,SAAE,CAAC;QACnC,QAAQ,GAAG,CAAC,CAAC;YACR,yBAAE,SAAF,iBAAE,UAAF,cAAE;;YAAP,GAAG,KAAE,SAAE,GAAI,QAAQ,CAAC,gBAAgB,CAAC,GAAG,sBAAnC,KAAE,IAAF,yBAAE,IAAF,KAAE,GAAF,SAAE,gBAAF,yBAAE;gBAAF,EAAE,GAAF,KAAE;gBACL,QAAQ,CAAC,IAAI,CAAC,EAAE;;;YADb,iBAAE;YAAF,cAAE;;;qBAAF,yBAAE,IAAF,SAAE;oBAAF,SAAE;;;oBAAF,iBAAE;0BAAF,cAAE;;;;IAGT,CAAC,MAAM,EAAE,EAAE,GAAG,CAAC,QAAQ,IAAI,IAAI,EAC7B,QAAQ,GAAG,CAAC;QAAA,GAAG;IAAA,CAAC;IAGlB,EAAE,EAAE,QAAQ,IAAI,IAAI,KAAK,QAAQ,CAAC,MAAM,EACtC,KAAK,CAAC,GAAG,CAAC,KAAK,CACZ,CAAU,WAAO,MAA2F,CAAhG,IAAI,EAAC,CAA2F;IAIjH,MAAM,CAAC,QAAQ;AACjB,CAAC;AAED,EAAwE,AAAxE,sEAAwE;AACxE,EAAE;AACF,EAA2E,AAA3E,yEAA2E;AAC3E,EAAwB,AAAxB,sBAAwB;AACxB,wCAAQ,CAAC,OAAO,GAAG,QAAQ,CAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;IAC1D,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,GACzB,MAAM,CAAC,QAAQ;SACV,EAAE,EAAE,QAAQ,IAAI,IAAI,EACzB,MAAM,CAAC,QAAQ;AAEnB,CAAC;AAED,EAAqC,AAArC,mCAAqC;AACrC,EAAE;AACF,EAA0E,AAA1E,wEAA0E;AAC1E,wCAAQ,CAAC,WAAW,GAAG,QAAQ,CAAE,IAAI,EAAE,aAAa,EAAE,CAAC;IACrD,EAAE,GAAG,aAAa,EAChB,MAAM,CAAC,IAAI;IACX,CAA+C,AAA/C,EAA+C,AAA/C,6CAA+C;IACjD,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,CAAG;IAEvC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI;IACxB,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,OAAO,UAAU,CAAE;QAE1C,yBAAa,SAAb,iBAAa,UAAb,cAAa;;QAAlB,GAAG,KAAE,SAAa,GAAI,aAAa,qBAA9B,KAAa,IAAb,yBAAa,IAAb,KAAa,GAAb,SAAa,gBAAb,yBAAa,QAAmB,CAAC;YAAjC,GAAG,CAAC,SAAS,GAAb,KAAa;YAChB,SAAS,GAAG,SAAS,CAAC,IAAI;YAC1B,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAG,IAAE,CAAC;gBAChC,EAAE,EACA,IAAI,CAAC,IAAI,CACN,WAAW,GACX,OAAO,CACN,SAAS,CAAC,WAAW,IACrB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,MAC/B,EAAE,EAEV,MAAM,CAAC,IAAI;YAEf,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,SAAS,GAAG,CAAC;gBACnC,EAA6C,AAA7C,2CAA6C;gBAC7C,EAAE,EAAE,YAAY,KAAK,SAAS,CAAC,OAAO,UAAU,CAAE,IAChD,MAAM,CAAC,IAAI;YAEf,CAAC,MAAM,CAAC;gBACN,EAAE,EAAE,QAAQ,KAAK,SAAS,EACxB,MAAM,CAAC,IAAI;YAEf,CAAC;QACH,CAAC;;QAvBI,iBAAa;QAAb,cAAa;;;iBAAb,yBAAa,IAAb,SAAa;gBAAb,SAAa;;;gBAAb,iBAAa;sBAAb,cAAa;;;;IAyBlB,MAAM,CAAC,KAAK;AACd,CAAC;AAED,EAAiB,AAAjB,eAAiB;AACjB,EAAE,EAAE,MAAM,CAAC,MAAM,KAAK,CAAW,cAAI,MAAM,KAAK,IAAI,EAClD,MAAM,CAAC,EAAE,CAAC,QAAQ,GAAG,QAAQ,CAAE,OAAO,EAAE,CAAC;IACvC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAI,CAAC;QAC5B,MAAM,CAAC,GAAG,CAAC,wCAAQ,CAAC,IAAI,EAAE,OAAO;IACnC,CAAC;AACH,CAAC;AAGH,EAA6B,AAA7B,2BAA6B;AAC7B,wCAAQ,CAAC,KAAK,GAAG,CAAO;AAExB,wCAAQ,CAAC,MAAM,GAAG,CAAQ;AAC1B,EAA8E,AAA9E,4EAA8E;AAC9E,EAAgB,AAAhB,cAAgB;AAChB,wCAAQ,CAAC,QAAQ,GAAG,wCAAQ,CAAC,MAAM;AAEnC,wCAAQ,CAAC,SAAS,GAAG,CAAW;AAChC,wCAAQ,CAAC,UAAU,GAAG,wCAAQ,CAAC,SAAS,CAAE,CAAQ,AAAR,EAAQ,AAAR,MAAQ;AAElD,wCAAQ,CAAC,QAAQ,GAAG,CAAU;AAC9B,wCAAQ,CAAC,KAAK,GAAG,CAAO;AACxB,wCAAQ,CAAC,OAAO,GAAG,CAAS;AAE5B,EAMG,AANH;;;;;;CAMG,AANH,EAMG,CAEH,EAA6C,AAA7C,2CAA6C;AAC7C,EAAuF,AAAvF,qFAAuF;AACvF,EAAuG,AAAvG,qGAAuG;AACvG,GAAG,CAAC,0CAAoB,GAAG,QAAQ,CAA/B,0CAAoB,CAAa,GAAG,EAAE,CAAC;IACzC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,YAAY;IACzB,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,aAAa;IAC1B,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAQ;IAC5C,MAAM,CAAC,KAAK,GAAG,CAAC;IAChB,MAAM,CAAC,MAAM,GAAG,EAAE;IAClB,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,CAAI;IAChC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;IACvB,GAAG,CAAY,GAA6B,GAA7B,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAArC,IAAI,GAAK,GAA6B,CAAtC,IAAI;IAEV,EAAsE,AAAtE,oEAAsE;IACtE,GAAG,CAAC,EAAE,GAAG,CAAC;IACV,GAAG,CAAC,EAAE,GAAG,EAAE;IACX,GAAG,CAAC,EAAE,GAAG,EAAE;UACJ,EAAE,GAAG,EAAE,CAAE,CAAC;QACf,GAAG,CAAC,KAAK,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;QAEjC,EAAE,EAAE,KAAK,KAAK,CAAC,EACb,EAAE,GAAG,EAAE;aAEP,EAAE,GAAG,EAAE;QAGT,EAAE,GAAI,EAAE,GAAG,EAAE,IAAK,CAAC;IACrB,CAAC;IACD,GAAG,CAAC,KAAK,GAAG,EAAE,GAAG,EAAE;IAEnB,EAAE,EAAE,KAAK,KAAK,CAAC,EACb,MAAM,CAAC,CAAC;SAER,MAAM,CAAC,KAAK;AAEhB,CAAC;AAED,EAAsC,AAAtC,oCAAsC;AACtC,EAAyC,AAAzC,uCAAyC;AACzC,GAAG,CAAC,qCAAe,GAAG,QAAQ,CAA1B,qCAAe,CAAa,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzE,GAAG,CAAC,eAAe,GAAG,0CAAoB,CAAC,GAAG;IAC9C,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,eAAe;AAC5E,CAAC;IAKK,iCAAW,GAHjB,EAAsB,AAAtB,oBAAsB;AACtB,EAAoD,AAApD,kDAAoD;AACpD,EAAmD,AAAnD,iDAAmD;cACnD,QAAQ;;aAAF,iCAAW;+CAAX,iCAAW;;kCAAX,iCAAW;;YACR,GAAS,EAAT,CAAS;mBAAhB,QAAQ,CAAD,SAAS,GAAG,CAAC;gBAClB,IAAI,CAAC,OAAO,GACV,CAAmE;YACvE,CAAC;;;YAEM,GAAQ,EAAR,CAAQ;mBAAf,QAAQ,CAAD,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACtB,GAAG,CAAC,MAAM,GAAG,CAAE;gBACf,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,CAAE;gBACb,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,CAAE;gBACb,GAAG,CAAC,CAAC,GAAG,CAAC;sBACF,IAAI,CAAE,CAAC;oBACZ,IAAI,GAAG,KAAK,CAAC,CAAC;oBACd,IAAI,GAAG,KAAK,CAAC,CAAC;oBACd,IAAI,GAAG,KAAK,CAAC,CAAC;oBACd,IAAI,GAAG,IAAI,IAAI,CAAC;oBAChB,IAAI,IAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;oBACrC,IAAI,IAAK,IAAI,GAAG,EAAE,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;oBACtC,IAAI,GAAG,IAAI,GAAG,EAAE;oBAChB,EAAE,EAAE,KAAK,CAAC,IAAI,GACZ,IAAI,GAAG,IAAI,GAAG,EAAE;yBACX,EAAE,EAAE,KAAK,CAAC,IAAI,GACnB,IAAI,GAAG,EAAE;oBAEX,MAAM,GACJ,MAAM,GACN,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,IACxB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI;oBAC1B,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;oBACvB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;oBAC9B,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GACpB,KAAK;gBAET,CAAC;gBACD,MAAM,CAAC,MAAM;YACf,CAAC;;;YAEM,GAAO,EAAP,CAAO;mBAAd,QAAQ,CAAD,OAAO,CAAC,cAAc,EAAE,iBAAiB,EAAE,CAAC;gBACjD,EAAE,GAAG,cAAc,CAAC,KAAK,CAAC,CAAyB,2BACjD,MAAM,CAAC,iBAAiB;gBAE1B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAC1B,cAAc,CAAC,OAAO,CAAC,CAAyB,0BAAE,CAAE;gBAEtD,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ;gBAC3C,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,QAAQ;gBAC7D,MAAM,CAAE,CAAuB,yBAAuB,MAAA,CAArB,IAAI,CAAC,QAAQ,CAAC,KAAK;YACtD,CAAC;;;YAEM,GAAgB,EAAhB,CAAgB;mBAAvB,QAAQ,CAAD,gBAAgB,CAAC,iBAAiB,EAAE,QAAQ,EAAE,CAAC;gBACpD,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ;gBAC1C,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,SAAS;gBAChE,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa;gBAC1C,MAAM,CAAC,OAAO;YAChB,CAAC;;;YAEM,GAAY,EAAZ,CAAY;mBAAnB,QAAQ,CAAD,YAAY,CAAC,QAAQ,EAAE,CAAC;gBAC7B,GAAG,CAAC,GAAG,GAAG,SAAS;gBACnB,GAAG,CAAC,CAAC,GAAG,CAAC;sBACF,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAE,CAAC;oBAC3B,GAAG,GAAG,QAAQ,CAAC,CAAC;oBAChB,EAAE,EAAG,GAAG,CAAC,CAAC,MAAM,GAAG,GAAK,GAAG,CAAC,CAAC,MAAM,GAAG,EACpC,MAAM,CAAC,GAAG;oBAEZ,CAAC;gBACH,CAAC;gBACD,MAAM,CAAC,CAAC,CAAC;YACX,CAAC;;;YAEM,GAAU,EAAV,CAAU;mBAAjB,QAAQ,CAAD,UAAU,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC;gBAC/C,GAAG,CAAC,SAAS,GAAG,iBAAiB,CAAC,OAAO,CAAC,CAAyB,0BAAE,CAAE;gBACvE,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS;gBACjC,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gBACtC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa;gBACpC,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa;gBACjC,GAAG,CAAC,KAAK,GAAG,GAAG;gBACf,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS;gBAC9B,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG;gBACxB,MAAM,CAAC,KAAK;YACd,CAAC;;;YAEM,GAAc,EAAd,CAAc;mBAArB,QAAQ,CAAD,cAAc,CAAC,aAAa,EAAE,CAAC;gBACpC,GAAG,CAAC,IAAI,GAAG,CAAC;gBACZ,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC;sBACV,IAAI,CAAE,CAAC;oBACZ,GAAG,CAAC,MAAM;oBACV,EAAE,EAAG,aAAa,CAAC,IAAI,MAAM,GAAG,GAAK,aAAa,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,EAClE,KAAK;oBAEP,EAAE,EAAG,aAAa,CAAC,IAAI,MAAM,GAAG,GAAK,aAAa,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,EAClE,IAAI,IAAI,CAAC;yBACJ,CAAC;wBACN,MAAM,GAAG,aAAa,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,aAAa,CAAC,IAAI,GAAG,CAAC;wBAC/D,GAAG,CAAC,QAAQ,GAAG,IAAI,GAAG,MAAM,GAAG,CAAC;wBAChC,GAAG,CAAC,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ;wBAC5C,QAAQ,CAAC,IAAI,CAAC,GAAG;wBACjB,IAAI,GAAG,QAAQ;oBACjB,CAAC;oBACD,EAAE,EAAE,IAAI,GAAG,aAAa,CAAC,MAAM,EAC7B,KAAK;gBAET,CAAC;gBACD,MAAM,CAAC,QAAQ;YACjB,CAAC;;;YAEM,GAAQ,EAAR,CAAQ;mBAAf,QAAQ,CAAD,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACtB,GAAG,CAAC,MAAM,GAAG,CAAE;gBACf,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,CAAE;gBACb,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,SAAS;gBACpB,GAAG,CAAC,IAAI,GAAG,CAAE;gBACb,GAAG,CAAC,CAAC,GAAG,CAAC;gBACT,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;gBACZ,EAA+D,AAA/D,6DAA+D;gBAC/D,GAAG,CAAC,UAAU;gBACd,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,GACvB,OAAO,CAAC,IAAI,CACV,CAAkJ;gBAGtJ,KAAK,GAAG,KAAK,CAAC,OAAO,wBAAwB,CAAE;sBACxC,IAAI,CAAE,CAAC;oBACZ,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,GAAI,IAAI,IAAI,CAAC,GAAK,IAAI,IAAI,CAAC;oBAC/B,IAAI,IAAK,IAAI,GAAG,EAAE,KAAK,CAAC,GAAK,IAAI,IAAI,CAAC;oBACtC,IAAI,IAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,IAAI;oBAC/B,GAAG,CAAC,IAAI,CAAC,IAAI;oBACb,EAAE,EAAE,IAAI,KAAK,EAAE,EACb,GAAG,CAAC,IAAI,CAAC,IAAI;oBAEf,EAAE,EAAE,IAAI,KAAK,EAAE,EACb,GAAG,CAAC,IAAI,CAAC,IAAI;oBAEf,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;oBACvB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,CAAE;oBAC9B,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GACpB,KAAK;gBAET,CAAC;gBACD,MAAM,CAAC,GAAG;YACZ,CAAC;;;WAzJG,iCAAW;;AA2JjB,iCAAW,CAAC,SAAS;AAErB,EAYG,AAZH;;;;;;;;;;;;CAYG,AAZH,EAYG,CAEH,EAAwB,AAAxB,sBAAwB;AACxB,EAAyB,AAAzB,uBAAyB;AACzB,GAAG,CAAC,mCAAa,GAAG,QAAQ,CAAxB,mCAAa,CAAa,GAAG,EAAE,EAAE,EAAE,CAAC;IACtC,GAAG,CAAC,IAAI,GAAG,KAAK;IAChB,GAAG,CAAC,GAAG,GAAG,IAAI;IACd,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ;IACtB,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,eAAe;IAC9B,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAkB,oBAAG,CAAa;IACnE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAqB,uBAAG,CAAa;IACtE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,gBAAgB,GAAG,CAAE,IAAG,CAAI;IAC1C,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAE,CAAC,EAAE,CAAC;QACvB,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,CAAkB,qBAAI,GAAG,CAAC,UAAU,KAAK,CAAU,WAChE,MAAM;SAEP,CAAC,CAAC,IAAI,KAAK,CAAM,QAAG,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK;QAC9D,EAAE,GAAG,IAAI,KAAK,IAAI,GAAG,IAAI,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC;IAEnC,CAAC;IAED,GAAG,CAAC,IAAI,GAAG,QAAQ,GAAI,CAAC;QACtB,GAAG,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,CAAC,CAAM;QACtB,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YACX,UAAU,CAAC,IAAI,EAAE,EAAE;YACnB,MAAM;QACR,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,CAAM;IACpB,CAAC;IAED,EAAE,EAAE,GAAG,CAAC,UAAU,KAAK,CAAU,WAAE,CAAC;QAClC,EAAE,EAAE,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3C,GAAG,CAAC,CAAC;gBACH,GAAG,IAAI,GAAG,CAAC,YAAY;YACzB,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC;YAAA,CAAC;YAClB,EAAE,EAAE,GAAG,EACL,IAAI;QAER,CAAC;QACD,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAkB,mBAAE,IAAI,EAAE,KAAK;QAC9C,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAkB,mBAAE,IAAI,EAAE,KAAK;QAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAM,OAAE,IAAI,EAAE,KAAK;IAC3C,CAAC;AACH,CAAC;SAEQ,+BAAS,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC;IACpC,MAAM,CAAC,MAAM,CAAC,KAAK,KAAK,CAAW,cAAI,KAAK,KAAK,IAAI,GACjD,SAAS,CAAC,KAAK,IACf,SAAS;AACf,CAAC;SACQ,qCAAe,CAAC,GAAG,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;IACpD,EAAE,EACA,MAAM,CAAC,GAAG,KAAK,CAAW,cAC1B,GAAG,KAAK,IAAI,IACZ,MAAM,CAAC,GAAG,CAAC,UAAU,MAAM,CAAU,WAErC,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,UAAU;SAEhC,MAAM,CAAC,SAAS;AAEpB,CAAC", "sources": ["src/dropzone.js", "src/emitter.js", "src/options.js", "node_modules/@parcel/runtime-js/lib/bundles/runtime-9fdc8cf257921628.js"], "sourcesContent": ["import extend from \"just-extend\";\nimport Emitter from \"./emitter\";\nimport defaultOptions from \"./options\";\n\nexport default class Dropzone extends Emitter {\n  static initClass() {\n    // Exposing the emitter class, mainly for tests\n    this.prototype.Emitter = Emitter;\n\n    /*\n     This is a list of all available events you can register on a dropzone object.\n\n     You can register an event handler like this:\n\n     dropzone.on(\"dragEnter\", function() { });\n\n     */\n    this.prototype.events = [\n      \"drop\",\n      \"dragstart\",\n      \"dragend\",\n      \"dragenter\",\n      \"dragover\",\n      \"dragleave\",\n      \"addedfile\",\n      \"addedfiles\",\n      \"removedfile\",\n      \"thumbnail\",\n      \"error\",\n      \"errormultiple\",\n      \"processing\",\n      \"processingmultiple\",\n      \"uploadprogress\",\n      \"totaluploadprogress\",\n      \"sending\",\n      \"sendingmultiple\",\n      \"success\",\n      \"successmultiple\",\n      \"canceled\",\n      \"canceledmultiple\",\n      \"complete\",\n      \"completemultiple\",\n      \"reset\",\n      \"maxfilesexceeded\",\n      \"maxfilesreached\",\n      \"queuecomplete\",\n    ];\n\n    this.prototype._thumbnailQueue = [];\n    this.prototype._processingThumbnail = false;\n  }\n\n  constructor(el, options) {\n    super();\n    let fallback, left;\n    this.element = el;\n\n    this.clickableElements = [];\n    this.listeners = [];\n    this.files = []; // All files\n\n    if (typeof this.element === \"string\") {\n      this.element = document.querySelector(this.element);\n    }\n\n    // Not checking if instance of HTMLElement or Element since IE9 is extremely weird.\n    if (!this.element || this.element.nodeType == null) {\n      throw new Error(\"Invalid dropzone element.\");\n    }\n\n    if (this.element.dropzone) {\n      throw new Error(\"Dropzone already attached.\");\n    }\n\n    // Now add this dropzone to the instances.\n    Dropzone.instances.push(this);\n\n    // Put the dropzone inside the element itself.\n    this.element.dropzone = this;\n\n    let elementOptions =\n      (left = Dropzone.optionsForElement(this.element)) != null ? left : {};\n\n    this.options = extend(\n      true,\n      {},\n      defaultOptions,\n      elementOptions,\n      options != null ? options : {}\n    );\n\n    this.options.previewTemplate = this.options.previewTemplate.replace(\n      /\\n*/g,\n      \"\"\n    );\n\n    // If the browser failed, just call the fallback and leave\n    if (this.options.forceFallback || !Dropzone.isBrowserSupported()) {\n      return this.options.fallback.call(this);\n    }\n\n    // @options.url = @element.getAttribute \"action\" unless @options.url?\n    if (this.options.url == null) {\n      this.options.url = this.element.getAttribute(\"action\");\n    }\n\n    if (!this.options.url) {\n      throw new Error(\"No URL provided.\");\n    }\n\n    if (this.options.acceptedFiles && this.options.acceptedMimeTypes) {\n      throw new Error(\n        \"You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.\"\n      );\n    }\n\n    if (this.options.uploadMultiple && this.options.chunking) {\n      throw new Error(\"You cannot set both: uploadMultiple and chunking.\");\n    }\n\n    if (this.options.binaryBody && this.options.uploadMultiple) {\n      throw new Error(\"You cannot set both: binaryBody and uploadMultiple.\");\n    }\n\n    // Backwards compatibility\n    if (this.options.acceptedMimeTypes) {\n      this.options.acceptedFiles = this.options.acceptedMimeTypes;\n      delete this.options.acceptedMimeTypes;\n    }\n\n    // Backwards compatibility\n    if (this.options.renameFilename != null) {\n      this.options.renameFile = (file) =>\n        this.options.renameFilename.call(this, file.name, file);\n    }\n\n    if (typeof this.options.method === \"string\") {\n      this.options.method = this.options.method.toUpperCase();\n    }\n\n    if ((fallback = this.getExistingFallback()) && fallback.parentNode) {\n      // Remove the fallback\n      fallback.parentNode.removeChild(fallback);\n    }\n\n    // Display previews in the previewsContainer element or the Dropzone element unless explicitly set to false\n    if (this.options.previewsContainer !== false) {\n      if (this.options.previewsContainer) {\n        this.previewsContainer = Dropzone.getElement(\n          this.options.previewsContainer,\n          \"previewsContainer\"\n        );\n      } else {\n        this.previewsContainer = this.element;\n      }\n    }\n\n    if (this.options.clickable) {\n      if (this.options.clickable === true) {\n        this.clickableElements = [this.element];\n      } else {\n        this.clickableElements = Dropzone.getElements(\n          this.options.clickable,\n          \"clickable\"\n        );\n      }\n    }\n\n    this.init();\n  }\n\n  // Returns all files that have been accepted\n  getAcceptedFiles() {\n    return this.files.filter((file) => file.accepted).map((file) => file);\n  }\n\n  // Returns all files that have been rejected\n  // Not sure when that's going to be useful, but added for completeness.\n  getRejectedFiles() {\n    return this.files.filter((file) => !file.accepted).map((file) => file);\n  }\n\n  getFilesWithStatus(status) {\n    return this.files\n      .filter((file) => file.status === status)\n      .map((file) => file);\n  }\n\n  // Returns all files that are in the queue\n  getQueuedFiles() {\n    return this.getFilesWithStatus(Dropzone.QUEUED);\n  }\n\n  getUploadingFiles() {\n    return this.getFilesWithStatus(Dropzone.UPLOADING);\n  }\n\n  getAddedFiles() {\n    return this.getFilesWithStatus(Dropzone.ADDED);\n  }\n\n  // Files that are either queued or uploading\n  getActiveFiles() {\n    return this.files\n      .filter(\n        (file) =>\n          file.status === Dropzone.UPLOADING || file.status === Dropzone.QUEUED\n      )\n      .map((file) => file);\n  }\n\n  // The function that gets called when Dropzone is initialized. You\n  // can (and should) setup event listeners inside this function.\n  init() {\n    // In case it isn't set already\n    if (this.element.tagName === \"form\") {\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n    }\n\n    if (\n      this.element.classList.contains(\"dropzone\") &&\n      !this.element.querySelector(\".dz-message\")\n    ) {\n      this.element.appendChild(\n        Dropzone.createElement(\n          `<div class=\"dz-default dz-message\"><button class=\"dz-button\" type=\"button\">${this.options.dictDefaultMessage}</button></div>`\n        )\n      );\n    }\n\n    if (this.clickableElements.length) {\n      let setupHiddenFileInput = () => {\n        if (this.hiddenFileInput) {\n          this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n        }\n        this.hiddenFileInput = document.createElement(\"input\");\n        this.hiddenFileInput.setAttribute(\"type\", \"file\");\n        if (this.options.maxFiles === null || this.options.maxFiles > 1) {\n          this.hiddenFileInput.setAttribute(\"multiple\", \"multiple\");\n        }\n        this.hiddenFileInput.className = \"dz-hidden-input\";\n\n        if (this.options.acceptedFiles !== null) {\n          this.hiddenFileInput.setAttribute(\n            \"accept\",\n            this.options.acceptedFiles\n          );\n        }\n        if (this.options.capture !== null) {\n          this.hiddenFileInput.setAttribute(\"capture\", this.options.capture);\n        }\n\n        // Making sure that no one can \"tab\" into this field.\n        this.hiddenFileInput.setAttribute(\"tabindex\", \"-1\");\n\n        // Not setting `display=\"none\"` because some browsers don't accept clicks\n        // on elements that aren't displayed.\n        this.hiddenFileInput.style.visibility = \"hidden\";\n        this.hiddenFileInput.style.position = \"absolute\";\n        this.hiddenFileInput.style.top = \"0\";\n        this.hiddenFileInput.style.left = \"0\";\n        this.hiddenFileInput.style.height = \"0\";\n        this.hiddenFileInput.style.width = \"0\";\n        Dropzone.getElement(\n          this.options.hiddenInputContainer,\n          \"hiddenInputContainer\"\n        ).appendChild(this.hiddenFileInput);\n        this.hiddenFileInput.addEventListener(\"change\", () => {\n          let { files } = this.hiddenFileInput;\n          if (files.length) {\n            for (let file of files) {\n              this.addFile(file);\n            }\n          }\n          this.emit(\"addedfiles\", files);\n          setupHiddenFileInput();\n        });\n      };\n      setupHiddenFileInput();\n    }\n\n    this.URL = window.URL !== null ? window.URL : window.webkitURL;\n\n    // Setup all event listeners on the Dropzone object itself.\n    // They're not in @setupEventListeners() because they shouldn't be removed\n    // again when the dropzone gets disabled.\n    for (let eventName of this.events) {\n      this.on(eventName, this.options[eventName]);\n    }\n\n    this.on(\"uploadprogress\", () => this.updateTotalUploadProgress());\n\n    this.on(\"removedfile\", () => this.updateTotalUploadProgress());\n\n    this.on(\"canceled\", (file) => this.emit(\"complete\", file));\n\n    // Emit a `queuecomplete` event if all files finished uploading.\n    this.on(\"complete\", (file) => {\n      if (\n        this.getAddedFiles().length === 0 &&\n        this.getUploadingFiles().length === 0 &&\n        this.getQueuedFiles().length === 0\n      ) {\n        // This needs to be deferred so that `queuecomplete` really triggers after `complete`\n        return setTimeout(() => this.emit(\"queuecomplete\"), 0);\n      }\n    });\n\n    const containsFiles = function (e) {\n      if (e.dataTransfer.types) {\n        // Because e.dataTransfer.types is an Object in\n        // IE, we need to iterate like this instead of\n        // using e.dataTransfer.types.some()\n        for (var i = 0; i < e.dataTransfer.types.length; i++) {\n          if (e.dataTransfer.types[i] === \"Files\") return true;\n        }\n      }\n      return false;\n    };\n\n    let noPropagation = function (e) {\n      // If there are no files, we don't want to stop\n      // propagation so we don't interfere with other\n      // drag and drop behaviour.\n      if (!containsFiles(e)) return;\n      e.stopPropagation();\n      if (e.preventDefault) {\n        return e.preventDefault();\n      } else {\n        return (e.returnValue = false);\n      }\n    };\n\n    // Create the listeners\n    this.listeners = [\n      {\n        element: this.element,\n        events: {\n          dragstart: (e) => {\n            return this.emit(\"dragstart\", e);\n          },\n          dragenter: (e) => {\n            noPropagation(e);\n            return this.emit(\"dragenter\", e);\n          },\n          dragover: (e) => {\n            // Makes it possible to drag files from chrome's download bar\n            // http://stackoverflow.com/questions/19526430/drag-and-drop-file-uploads-from-chrome-downloads-bar\n            // Try is required to prevent bug in Internet Explorer 11 (SCRIPT65535 exception)\n            let efct;\n            try {\n              efct = e.dataTransfer.effectAllowed;\n            } catch (error) {}\n            e.dataTransfer.dropEffect =\n              \"move\" === efct || \"linkMove\" === efct ? \"move\" : \"copy\";\n\n            noPropagation(e);\n            return this.emit(\"dragover\", e);\n          },\n          dragleave: (e) => {\n            return this.emit(\"dragleave\", e);\n          },\n          drop: (e) => {\n            noPropagation(e);\n            return this.drop(e);\n          },\n          dragend: (e) => {\n            return this.emit(\"dragend\", e);\n          },\n        },\n\n        // This is disabled right now, because the browsers don't implement it properly.\n        // \"paste\": (e) =>\n        //   noPropagation e\n        //   @paste e\n      },\n    ];\n\n    this.clickableElements.forEach((clickableElement) => {\n      return this.listeners.push({\n        element: clickableElement,\n        events: {\n          click: (evt) => {\n            // Only the actual dropzone or the message element should trigger file selection\n            if (\n              clickableElement !== this.element ||\n              evt.target === this.element ||\n              Dropzone.elementInside(\n                evt.target,\n                this.element.querySelector(\".dz-message\")\n              )\n            ) {\n              this.hiddenFileInput.click(); // Forward the click\n            }\n            return true;\n          },\n        },\n      });\n    });\n\n    this.enable();\n\n    return this.options.init.call(this);\n  }\n\n  // Not fully tested yet\n  destroy() {\n    this.disable();\n    this.removeAllFiles(true);\n    if (\n      this.hiddenFileInput != null ? this.hiddenFileInput.parentNode : undefined\n    ) {\n      this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n      this.hiddenFileInput = null;\n    }\n    delete this.element.dropzone;\n    return Dropzone.instances.splice(Dropzone.instances.indexOf(this), 1);\n  }\n\n  updateTotalUploadProgress() {\n    let totalUploadProgress;\n    let totalBytesSent = 0;\n    let totalBytes = 0;\n\n    let activeFiles = this.getActiveFiles();\n\n    if (activeFiles.length) {\n      for (let file of this.getActiveFiles()) {\n        totalBytesSent += file.upload.bytesSent;\n        totalBytes += file.upload.total;\n      }\n      totalUploadProgress = (100 * totalBytesSent) / totalBytes;\n    } else {\n      totalUploadProgress = 100;\n    }\n\n    return this.emit(\n      \"totaluploadprogress\",\n      totalUploadProgress,\n      totalBytes,\n      totalBytesSent\n    );\n  }\n\n  // @options.paramName can be a function taking one parameter rather than a string.\n  // A parameter name for a file is obtained simply by calling this with an index number.\n  _getParamName(n) {\n    if (typeof this.options.paramName === \"function\") {\n      return this.options.paramName(n);\n    } else {\n      return `${this.options.paramName}${\n        this.options.uploadMultiple ? `[${n}]` : \"\"\n      }`;\n    }\n  }\n\n  // If @options.renameFile is a function,\n  // the function will be used to rename the file.name before appending it to the formData\n  _renameFile(file) {\n    if (typeof this.options.renameFile !== \"function\") {\n      return file.name;\n    }\n    return this.options.renameFile(file);\n  }\n\n  // Returns a form that can be used as fallback if the browser does not support DragnDrop\n  //\n  // If the dropzone is already a form, only the input field and button are returned. Otherwise a complete form element is provided.\n  // This code has to pass in IE7 :(\n  getFallbackForm() {\n    let existingFallback, form;\n    if ((existingFallback = this.getExistingFallback())) {\n      return existingFallback;\n    }\n\n    let fieldsString = '<div class=\"dz-fallback\">';\n    if (this.options.dictFallbackText) {\n      fieldsString += `<p>${this.options.dictFallbackText}</p>`;\n    }\n    fieldsString += `<input type=\"file\" name=\"${this._getParamName(0)}\" ${\n      this.options.uploadMultiple ? 'multiple=\"multiple\"' : undefined\n    } /><input type=\"submit\" value=\"Upload!\"></div>`;\n\n    let fields = Dropzone.createElement(fieldsString);\n    if (this.element.tagName !== \"FORM\") {\n      form = Dropzone.createElement(\n        `<form action=\"${this.options.url}\" enctype=\"multipart/form-data\" method=\"${this.options.method}\"></form>`\n      );\n      form.appendChild(fields);\n    } else {\n      // Make sure that the enctype and method attributes are set properly\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n      this.element.setAttribute(\"method\", this.options.method);\n    }\n    return form != null ? form : fields;\n  }\n\n  // Returns the fallback elements if they exist already\n  //\n  // This code has to pass in IE7 :(\n  getExistingFallback() {\n    let getFallback = function (elements) {\n      for (let el of elements) {\n        if (/(^| )fallback($| )/.test(el.className)) {\n          return el;\n        }\n      }\n    };\n\n    for (let tagName of [\"div\", \"form\"]) {\n      var fallback;\n      if (\n        (fallback = getFallback(this.element.getElementsByTagName(tagName)))\n      ) {\n        return fallback;\n      }\n    }\n  }\n\n  // Activates all listeners stored in @listeners\n  setupEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.addEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Deactivates all listeners stored in @listeners\n  removeEventListeners() {\n    return this.listeners.map((elementListeners) =>\n      (() => {\n        let result = [];\n        for (let event in elementListeners.events) {\n          let listener = elementListeners.events[event];\n          result.push(\n            elementListeners.element.removeEventListener(event, listener, false)\n          );\n        }\n        return result;\n      })()\n    );\n  }\n\n  // Removes all event listeners and cancels all files in the queue or being processed.\n  disable() {\n    this.clickableElements.forEach((element) =>\n      element.classList.remove(\"dz-clickable\")\n    );\n    this.removeEventListeners();\n    this.disabled = true;\n\n    return this.files.map((file) => this.cancelUpload(file));\n  }\n\n  enable() {\n    delete this.disabled;\n    this.clickableElements.forEach((element) =>\n      element.classList.add(\"dz-clickable\")\n    );\n    return this.setupEventListeners();\n  }\n\n  // Returns a nicely formatted filesize\n  filesize(size) {\n    let selectedSize = 0;\n    let selectedUnit = \"b\";\n\n    if (size > 0) {\n      let units = [\"tb\", \"gb\", \"mb\", \"kb\", \"b\"];\n\n      for (let i = 0; i < units.length; i++) {\n        let unit = units[i];\n        let cutoff = Math.pow(this.options.filesizeBase, 4 - i) / 10;\n\n        if (size >= cutoff) {\n          selectedSize = size / Math.pow(this.options.filesizeBase, 4 - i);\n          selectedUnit = unit;\n          break;\n        }\n      }\n\n      selectedSize = Math.round(10 * selectedSize) / 10; // Cutting of digits\n    }\n\n    return `<strong>${selectedSize}</strong> ${this.options.dictFileSizeUnits[selectedUnit]}`;\n  }\n\n  // Adds or removes the `dz-max-files-reached` class from the form.\n  _updateMaxFilesReachedClass() {\n    if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      if (this.getAcceptedFiles().length === this.options.maxFiles) {\n        this.emit(\"maxfilesreached\", this.files);\n      }\n      return this.element.classList.add(\"dz-max-files-reached\");\n    } else {\n      return this.element.classList.remove(\"dz-max-files-reached\");\n    }\n  }\n\n  drop(e) {\n    if (!e.dataTransfer) {\n      return;\n    }\n    this.emit(\"drop\", e);\n\n    // Convert the FileList to an Array\n    // This is necessary for IE11\n    let files = [];\n    for (let i = 0; i < e.dataTransfer.files.length; i++) {\n      files[i] = e.dataTransfer.files[i];\n    }\n\n    // Even if it's a folder, files.length will contain the folders.\n    if (files.length) {\n      let { items } = e.dataTransfer;\n      if (items && items.length && items[0].webkitGetAsEntry != null) {\n        // The browser supports dropping of folders, so handle items instead of files\n        this._addFilesFromItems(items);\n      } else {\n        this.handleFiles(files);\n      }\n    }\n\n    this.emit(\"addedfiles\", files);\n  }\n\n  paste(e) {\n    if (\n      __guard__(e != null ? e.clipboardData : undefined, (x) => x.items) == null\n    ) {\n      return;\n    }\n\n    this.emit(\"paste\", e);\n    let { items } = e.clipboardData;\n\n    if (items.length) {\n      return this._addFilesFromItems(items);\n    }\n  }\n\n  handleFiles(files) {\n    for (let file of files) {\n      this.addFile(file);\n    }\n  }\n\n  // When a folder is dropped (or files are pasted), items must be handled\n  // instead of files.\n  _addFilesFromItems(items) {\n    return (() => {\n      let result = [];\n      for (let item of items) {\n        var entry;\n        if (\n          item.webkitGetAsEntry != null &&\n          (entry = item.webkitGetAsEntry())\n        ) {\n          if (entry.isFile) {\n            result.push(this.addFile(item.getAsFile()));\n          } else if (entry.isDirectory) {\n            // Append all files from that directory to files\n            result.push(this._addFilesFromDirectory(entry, entry.name));\n          } else {\n            result.push(undefined);\n          }\n        } else if (item.getAsFile != null) {\n          if (item.kind == null || item.kind === \"file\") {\n            result.push(this.addFile(item.getAsFile()));\n          } else {\n            result.push(undefined);\n          }\n        } else {\n          result.push(undefined);\n        }\n      }\n      return result;\n    })();\n  }\n\n  // Goes through the directory, and adds each file it finds recursively\n  _addFilesFromDirectory(directory, path) {\n    let dirReader = directory.createReader();\n\n    let errorHandler = (error) =>\n      __guardMethod__(console, \"log\", (o) => o.log(error));\n\n    var readEntries = () => {\n      return dirReader.readEntries((entries) => {\n        if (entries.length > 0) {\n          for (let entry of entries) {\n            if (entry.isFile) {\n              entry.file((file) => {\n                if (\n                  this.options.ignoreHiddenFiles &&\n                  file.name.substring(0, 1) === \".\"\n                ) {\n                  return;\n                }\n                file.fullPath = `${path}/${file.name}`;\n                return this.addFile(file);\n              });\n            } else if (entry.isDirectory) {\n              this._addFilesFromDirectory(entry, `${path}/${entry.name}`);\n            }\n          }\n\n          // Recursively call readEntries() again, since browser only handle\n          // the first 100 entries.\n          // See: https://developer.mozilla.org/en-US/docs/Web/API/DirectoryReader#readEntries\n          readEntries();\n        }\n        return null;\n      }, errorHandler);\n    };\n\n    return readEntries();\n  }\n\n  // If `done()` is called without argument the file is accepted\n  // If you call it with an error message, the file is rejected\n  // (This allows for asynchronous validation)\n  //\n  // This function checks the filesize, and if the file.type passes the\n  // `acceptedFiles` check.\n  accept(file, done) {\n    if (\n      this.options.maxFilesize &&\n      file.size > this.options.maxFilesize * 1024 * 1024\n    ) {\n      done(\n        this.options.dictFileTooBig\n          .replace(\"{{filesize}}\", Math.round(file.size / 1024 / 10.24) / 100)\n          .replace(\"{{maxFilesize}}\", this.options.maxFilesize)\n      );\n    } else if (!Dropzone.isValidFile(file, this.options.acceptedFiles)) {\n      done(this.options.dictInvalidFileType);\n    } else if (\n      this.options.maxFiles != null &&\n      this.getAcceptedFiles().length >= this.options.maxFiles\n    ) {\n      done(\n        this.options.dictMaxFilesExceeded.replace(\n          \"{{maxFiles}}\",\n          this.options.maxFiles\n        )\n      );\n      this.emit(\"maxfilesexceeded\", file);\n    } else {\n      this.options.accept.call(this, file, done);\n    }\n  }\n\n  addFile(file) {\n    file.upload = {\n      uuid: Dropzone.uuidv4(),\n      progress: 0,\n      // Setting the total upload size to file.size for the beginning\n      // It's actual different than the size to be transmitted.\n      total: file.size,\n      bytesSent: 0,\n      filename: this._renameFile(file),\n      // Not setting chunking information here, because the acutal data — and\n      // thus the chunks — might change if `options.transformFile` is set\n      // and does something to the data.\n    };\n    this.files.push(file);\n\n    file.status = Dropzone.ADDED;\n\n    this.emit(\"addedfile\", file);\n\n    this._enqueueThumbnail(file);\n\n    this.accept(file, (error) => {\n      if (error) {\n        file.accepted = false;\n        this._errorProcessing([file], error); // Will set the file.status\n      } else {\n        file.accepted = true;\n        if (this.options.autoQueue) {\n          this.enqueueFile(file);\n        } // Will set .accepted = true\n      }\n      this._updateMaxFilesReachedClass();\n    });\n  }\n\n  // Wrapper for enqueueFile\n  enqueueFiles(files) {\n    for (let file of files) {\n      this.enqueueFile(file);\n    }\n    return null;\n  }\n\n  enqueueFile(file) {\n    if (file.status === Dropzone.ADDED && file.accepted === true) {\n      file.status = Dropzone.QUEUED;\n      if (this.options.autoProcessQueue) {\n        return setTimeout(() => this.processQueue(), 0); // Deferring the call\n      }\n    } else {\n      throw new Error(\n        \"This file can't be queued because it has already been processed or was rejected.\"\n      );\n    }\n  }\n\n  _enqueueThumbnail(file) {\n    if (\n      this.options.createImageThumbnails &&\n      file.type.match(/image.*/) &&\n      file.size <= this.options.maxThumbnailFilesize * 1024 * 1024\n    ) {\n      this._thumbnailQueue.push(file);\n      return setTimeout(() => this._processThumbnailQueue(), 0); // Deferring the call\n    }\n  }\n\n  _processThumbnailQueue() {\n    if (this._processingThumbnail || this._thumbnailQueue.length === 0) {\n      return;\n    }\n\n    this._processingThumbnail = true;\n    let file = this._thumbnailQueue.shift();\n    return this.createThumbnail(\n      file,\n      this.options.thumbnailWidth,\n      this.options.thumbnailHeight,\n      this.options.thumbnailMethod,\n      true,\n      (dataUrl) => {\n        this.emit(\"thumbnail\", file, dataUrl);\n        this._processingThumbnail = false;\n        return this._processThumbnailQueue();\n      }\n    );\n  }\n\n  // Can be called by the user to remove a file\n  removeFile(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      this.cancelUpload(file);\n    }\n    this.files = without(this.files, file);\n\n    this.emit(\"removedfile\", file);\n    if (this.files.length === 0) {\n      return this.emit(\"reset\");\n    }\n  }\n\n  // Removes all files that aren't currently processed from the list\n  removeAllFiles(cancelIfNecessary) {\n    // Create a copy of files since removeFile() changes the @files array.\n    if (cancelIfNecessary == null) {\n      cancelIfNecessary = false;\n    }\n    for (let file of this.files.slice()) {\n      if (file.status !== Dropzone.UPLOADING || cancelIfNecessary) {\n        this.removeFile(file);\n      }\n    }\n    return null;\n  }\n\n  // Resizes an image before it gets sent to the server. This function is the default behavior of\n  // `options.transformFile` if `resizeWidth` or `resizeHeight` are set. The callback is invoked with\n  // the resized blob.\n  resizeImage(file, width, height, resizeMethod, callback) {\n    return this.createThumbnail(\n      file,\n      width,\n      height,\n      resizeMethod,\n      true,\n      (dataUrl, canvas) => {\n        if (canvas == null) {\n          // The image has not been resized\n          return callback(file);\n        } else {\n          let { resizeMimeType } = this.options;\n          if (resizeMimeType == null) {\n            resizeMimeType = file.type;\n          }\n          let resizedDataURL = canvas.toDataURL(\n            resizeMimeType,\n            this.options.resizeQuality\n          );\n          if (\n            resizeMimeType === \"image/jpeg\" ||\n            resizeMimeType === \"image/jpg\"\n          ) {\n            // Now add the original EXIF information\n            resizedDataURL = ExifRestore.restore(file.dataURL, resizedDataURL);\n          }\n          return callback(Dropzone.dataURItoBlob(resizedDataURL));\n        }\n      }\n    );\n  }\n\n  createThumbnail(file, width, height, resizeMethod, fixOrientation, callback) {\n    let fileReader = new FileReader();\n\n    fileReader.onload = () => {\n      file.dataURL = fileReader.result;\n\n      // Don't bother creating a thumbnail for SVG images since they're vector\n      if (file.type === \"image/svg+xml\") {\n        if (callback != null) {\n          callback(fileReader.result);\n        }\n        return;\n      }\n\n      this.createThumbnailFromUrl(\n        file,\n        width,\n        height,\n        resizeMethod,\n        fixOrientation,\n        callback\n      );\n    };\n\n    fileReader.readAsDataURL(file);\n  }\n\n  // `mockFile` needs to have these attributes:\n  //\n  //     { name: 'name', size: 12345, imageUrl: '' }\n  //\n  // `callback` will be invoked when the image has been downloaded and displayed.\n  // `crossOrigin` will be added to the `img` tag when accessing the file.\n  displayExistingFile(\n    mockFile,\n    imageUrl,\n    callback,\n    crossOrigin,\n    resizeThumbnail = true\n  ) {\n    this.emit(\"addedfile\", mockFile);\n    this.emit(\"complete\", mockFile);\n\n    if (!resizeThumbnail) {\n      this.emit(\"thumbnail\", mockFile, imageUrl);\n      if (callback) callback();\n    } else {\n      let onDone = (thumbnail) => {\n        this.emit(\"thumbnail\", mockFile, thumbnail);\n        if (callback) callback();\n      };\n      mockFile.dataURL = imageUrl;\n\n      this.createThumbnailFromUrl(\n        mockFile,\n        this.options.thumbnailWidth,\n        this.options.thumbnailHeight,\n        this.options.thumbnailMethod,\n        this.options.fixOrientation,\n        onDone,\n        crossOrigin\n      );\n    }\n  }\n\n  createThumbnailFromUrl(\n    file,\n    width,\n    height,\n    resizeMethod,\n    fixOrientation,\n    callback,\n    crossOrigin\n  ) {\n    // Not using `new Image` here because of a bug in latest Chrome versions.\n    // See https://github.com/enyo/dropzone/pull/226\n    let img = document.createElement(\"img\");\n\n    if (crossOrigin) {\n      img.crossOrigin = crossOrigin;\n    }\n\n    // fixOrientation is not needed anymore with browsers handling imageOrientation\n    fixOrientation =\n      getComputedStyle(document.body)[\"imageOrientation\"] == \"from-image\"\n        ? false\n        : fixOrientation;\n\n    img.onload = () => {\n      let loadExif = (callback) => callback(1);\n      if (typeof EXIF !== \"undefined\" && EXIF !== null && fixOrientation) {\n        loadExif = (callback) =>\n          EXIF.getData(img, function () {\n            return callback(EXIF.getTag(this, \"Orientation\"));\n          });\n      }\n\n      return loadExif((orientation) => {\n        file.width = img.width;\n        file.height = img.height;\n\n        let resizeInfo = this.options.resize.call(\n          this,\n          file,\n          width,\n          height,\n          resizeMethod\n        );\n\n        let canvas = document.createElement(\"canvas\");\n        let ctx = canvas.getContext(\"2d\");\n\n        canvas.width = resizeInfo.trgWidth;\n        canvas.height = resizeInfo.trgHeight;\n\n        if (orientation > 4) {\n          canvas.width = resizeInfo.trgHeight;\n          canvas.height = resizeInfo.trgWidth;\n        }\n\n        switch (orientation) {\n          case 2:\n            // horizontal flip\n            ctx.translate(canvas.width, 0);\n            ctx.scale(-1, 1);\n            break;\n          case 3:\n            // 180° rotate left\n            ctx.translate(canvas.width, canvas.height);\n            ctx.rotate(Math.PI);\n            break;\n          case 4:\n            // vertical flip\n            ctx.translate(0, canvas.height);\n            ctx.scale(1, -1);\n            break;\n          case 5:\n            // vertical flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.scale(1, -1);\n            break;\n          case 6:\n            // 90° rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(0, -canvas.width);\n            break;\n          case 7:\n            // horizontal flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(canvas.height, -canvas.width);\n            ctx.scale(-1, 1);\n            break;\n          case 8:\n            // 90° rotate left\n            ctx.rotate(-0.5 * Math.PI);\n            ctx.translate(-canvas.height, 0);\n            break;\n        }\n\n        // This is a bugfix for iOS' scaling bug.\n        drawImageIOSFix(\n          ctx,\n          img,\n          resizeInfo.srcX != null ? resizeInfo.srcX : 0,\n          resizeInfo.srcY != null ? resizeInfo.srcY : 0,\n          resizeInfo.srcWidth,\n          resizeInfo.srcHeight,\n          resizeInfo.trgX != null ? resizeInfo.trgX : 0,\n          resizeInfo.trgY != null ? resizeInfo.trgY : 0,\n          resizeInfo.trgWidth,\n          resizeInfo.trgHeight\n        );\n\n        let thumbnail = canvas.toDataURL(\"image/png\");\n\n        if (callback != null) {\n          return callback(thumbnail, canvas);\n        }\n      });\n    };\n\n    if (callback != null) {\n      img.onerror = callback;\n    }\n\n    return (img.src = file.dataURL);\n  }\n\n  // Goes through the queue and processes files if there aren't too many already.\n  processQueue() {\n    let { parallelUploads } = this.options;\n    let processingLength = this.getUploadingFiles().length;\n    let i = processingLength;\n\n    // There are already at least as many files uploading than should be\n    if (processingLength >= parallelUploads) {\n      return;\n    }\n\n    let queuedFiles = this.getQueuedFiles();\n\n    if (!(queuedFiles.length > 0)) {\n      return;\n    }\n\n    if (this.options.uploadMultiple) {\n      // The files should be uploaded in one request\n      return this.processFiles(\n        queuedFiles.slice(0, parallelUploads - processingLength)\n      );\n    } else {\n      while (i < parallelUploads) {\n        if (!queuedFiles.length) {\n          return;\n        } // Nothing left to process\n        this.processFile(queuedFiles.shift());\n        i++;\n      }\n    }\n  }\n\n  // Wrapper for `processFiles`\n  processFile(file) {\n    return this.processFiles([file]);\n  }\n\n  // Loads the file, then calls finishedLoading()\n  processFiles(files) {\n    for (let file of files) {\n      file.processing = true; // Backwards compatibility\n      file.status = Dropzone.UPLOADING;\n\n      this.emit(\"processing\", file);\n    }\n\n    if (this.options.uploadMultiple) {\n      this.emit(\"processingmultiple\", files);\n    }\n\n    return this.uploadFiles(files);\n  }\n\n  _getFilesWithXhr(xhr) {\n    let files;\n    return (files = this.files\n      .filter((file) => file.xhr === xhr)\n      .map((file) => file));\n  }\n\n  // Cancels the file upload and sets the status to CANCELED\n  // **if** the file is actually being uploaded.\n  // If it's still in the queue, the file is being removed from it and the status\n  // set to CANCELED.\n  cancelUpload(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      let groupedFiles = this._getFilesWithXhr(file.xhr);\n      for (let groupedFile of groupedFiles) {\n        groupedFile.status = Dropzone.CANCELED;\n      }\n      if (typeof file.xhr !== \"undefined\") {\n        file.xhr.abort();\n      }\n      for (let groupedFile of groupedFiles) {\n        this.emit(\"canceled\", groupedFile);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", groupedFiles);\n      }\n    } else if (\n      file.status === Dropzone.ADDED ||\n      file.status === Dropzone.QUEUED\n    ) {\n      file.status = Dropzone.CANCELED;\n      this.emit(\"canceled\", file);\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", [file]);\n      }\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  resolveOption(option, ...args) {\n    if (typeof option === \"function\") {\n      return option.apply(this, args);\n    }\n    return option;\n  }\n\n  uploadFile(file) {\n    return this.uploadFiles([file]);\n  }\n\n  uploadFiles(files) {\n    this._transformFiles(files, (transformedFiles) => {\n      if (this.options.chunking) {\n        // Chunking is not allowed to be used with `uploadMultiple` so we know\n        // that there is only __one__file.\n        let transformedFile = transformedFiles[0];\n        files[0].upload.chunked =\n          this.options.chunking &&\n          (this.options.forceChunking ||\n            transformedFile.size > this.options.chunkSize);\n        files[0].upload.totalChunkCount = Math.ceil(\n          transformedFile.size / this.options.chunkSize\n        );\n      }\n\n      if (files[0].upload.chunked) {\n        // This file should be sent in chunks!\n\n        // If the chunking option is set, we **know** that there can only be **one** file, since\n        // uploadMultiple is not allowed with this option.\n        let file = files[0];\n        let transformedFile = transformedFiles[0];\n        let startedChunkCount = 0;\n\n        file.upload.chunks = [];\n\n        let handleNextChunk = () => {\n          let chunkIndex = 0;\n\n          // Find the next item in file.upload.chunks that is not defined yet.\n          while (file.upload.chunks[chunkIndex] !== undefined) {\n            chunkIndex++;\n          }\n\n          // This means, that all chunks have already been started.\n          if (chunkIndex >= file.upload.totalChunkCount) return;\n\n          startedChunkCount++;\n\n          let start = chunkIndex * this.options.chunkSize;\n          let end = Math.min(\n            start + this.options.chunkSize,\n            transformedFile.size\n          );\n\n          let dataBlock = {\n            name: this._getParamName(0),\n            data: transformedFile.webkitSlice\n              ? transformedFile.webkitSlice(start, end)\n              : transformedFile.slice(start, end),\n            filename: file.upload.filename,\n            chunkIndex: chunkIndex,\n          };\n\n          file.upload.chunks[chunkIndex] = {\n            file: file,\n            index: chunkIndex,\n            dataBlock: dataBlock, // In case we want to retry.\n            status: Dropzone.UPLOADING,\n            progress: 0,\n            retries: 0, // The number of times this block has been retried.\n          };\n\n          this._uploadData(files, [dataBlock]);\n        };\n\n        file.upload.finishedChunkUpload = (chunk, response) => {\n          let allFinished = true;\n          chunk.status = Dropzone.SUCCESS;\n\n          // Clear the data from the chunk\n          chunk.dataBlock = null;\n          chunk.response = chunk.xhr.responseText;\n          chunk.responseHeaders = chunk.xhr.getAllResponseHeaders();\n          // Leaving this reference to xhr will cause memory leaks.\n          chunk.xhr = null;\n\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            if (file.upload.chunks[i] === undefined) {\n              return handleNextChunk();\n            }\n            if (file.upload.chunks[i].status !== Dropzone.SUCCESS) {\n              allFinished = false;\n            }\n          }\n\n          if (allFinished) {\n            this.options.chunksUploaded(file, () => {\n              this._finished(files, response, null);\n            });\n          }\n        };\n\n        if (this.options.parallelChunkUploads) {\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            handleNextChunk();\n          }\n        } else {\n          handleNextChunk();\n        }\n      } else {\n        let dataBlocks = [];\n        for (let i = 0; i < files.length; i++) {\n          dataBlocks[i] = {\n            name: this._getParamName(i),\n            data: transformedFiles[i],\n            filename: files[i].upload.filename,\n          };\n        }\n        this._uploadData(files, dataBlocks);\n      }\n    });\n  }\n\n  /// Returns the right chunk for given file and xhr\n  _getChunk(file, xhr) {\n    for (let i = 0; i < file.upload.totalChunkCount; i++) {\n      if (\n        file.upload.chunks[i] !== undefined &&\n        file.upload.chunks[i].xhr === xhr\n      ) {\n        return file.upload.chunks[i];\n      }\n    }\n  }\n\n  // This function actually uploads the file(s) to the server.\n  //\n  //  If dataBlocks contains the actual data to upload (meaning, that this could\n  // either be transformed files, or individual chunks for chunked upload) then\n  // they will be used for the actual data to upload.\n  _uploadData(files, dataBlocks) {\n    let xhr = new XMLHttpRequest();\n\n    // Put the xhr object in the file objects to be able to reference it later.\n    for (let file of files) {\n      file.xhr = xhr;\n    }\n    if (files[0].upload.chunked) {\n      // Put the xhr object in the right chunk object, so it can be associated\n      // later, and found with _getChunk.\n      files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr = xhr;\n    }\n\n    let method = this.resolveOption(this.options.method, files, dataBlocks);\n    let url = this.resolveOption(this.options.url, files, dataBlocks);\n    xhr.open(method, url, true);\n\n    // Setting the timeout after open because of IE11 issue: https://gitlab.com/meno/dropzone/issues/8\n    let timeout = this.resolveOption(this.options.timeout, files);\n    if (timeout) xhr.timeout = this.resolveOption(this.options.timeout, files);\n\n    // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n    xhr.withCredentials = !!this.options.withCredentials;\n\n    xhr.onload = (e) => {\n      this._finishedUploading(files, xhr, e);\n    };\n\n    xhr.ontimeout = () => {\n      this._handleUploadError(\n        files,\n        xhr,\n        `Request timedout after ${this.options.timeout / 1000} seconds`\n      );\n    };\n\n    xhr.onerror = () => {\n      this._handleUploadError(files, xhr);\n    };\n\n    // Some browsers do not have the .upload property\n    let progressObj = xhr.upload != null ? xhr.upload : xhr;\n    progressObj.onprogress = (e) =>\n      this._updateFilesUploadProgress(files, xhr, e);\n\n    let headers = this.options.defaultHeaders\n      ? {\n          Accept: \"application/json\",\n          \"Cache-Control\": \"no-cache\",\n          \"X-Requested-With\": \"XMLHttpRequest\",\n        }\n      : {};\n\n    if (this.options.binaryBody) {\n      headers[\"Content-Type\"] = files[0].type;\n    }\n\n    if (this.options.headers) {\n      extend(headers, this.options.headers);\n    }\n\n    for (let headerName in headers) {\n      let headerValue = headers[headerName];\n      if (headerValue) {\n        xhr.setRequestHeader(headerName, headerValue);\n      }\n    }\n\n    if (this.options.binaryBody) {\n      // Since the file is going to be sent as binary body, it doesn't make\n      // any sense to generate `FormData` for it.\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr);\n      }\n      this.submitRequest(xhr, null, files);\n    } else {\n      let formData = new FormData();\n\n      // Adding all @options parameters\n      if (this.options.params) {\n        let additionalParams = this.options.params;\n        if (typeof additionalParams === \"function\") {\n          additionalParams = additionalParams.call(\n            this,\n            files,\n            xhr,\n            files[0].upload.chunked ? this._getChunk(files[0], xhr) : null\n          );\n        }\n\n        for (let key in additionalParams) {\n          let value = additionalParams[key];\n          if (Array.isArray(value)) {\n            // The additional parameter contains an array,\n            // so lets iterate over it to attach each value\n            // individually.\n            for (let i = 0; i < value.length; i++) {\n              formData.append(key, value[i]);\n            }\n          } else {\n            formData.append(key, value);\n          }\n        }\n      }\n\n      // Let the user add additional data if necessary\n      for (let file of files) {\n        this.emit(\"sending\", file, xhr, formData);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"sendingmultiple\", files, xhr, formData);\n      }\n\n      this._addFormElementData(formData);\n\n      // Finally add the files\n      // Has to be last because some servers (eg: S3) expect the file to be the last parameter\n      for (let i = 0; i < dataBlocks.length; i++) {\n        let dataBlock = dataBlocks[i];\n        formData.append(dataBlock.name, dataBlock.data, dataBlock.filename);\n      }\n\n      this.submitRequest(xhr, formData, files);\n    }\n  }\n\n  // Transforms all files with this.options.transformFile and invokes done with the transformed files when done.\n  _transformFiles(files, done) {\n    let transformedFiles = [];\n    // Clumsy way of handling asynchronous calls, until I get to add a proper Future library.\n    let doneCounter = 0;\n    for (let i = 0; i < files.length; i++) {\n      this.options.transformFile.call(this, files[i], (transformedFile) => {\n        transformedFiles[i] = transformedFile;\n        if (++doneCounter === files.length) {\n          done(transformedFiles);\n        }\n      });\n    }\n  }\n\n  // Takes care of adding other input elements of the form to the AJAX request\n  _addFormElementData(formData) {\n    // Take care of other input elements\n    if (this.element.tagName === \"FORM\") {\n      for (let input of this.element.querySelectorAll(\n        \"input, textarea, select, button\"\n      )) {\n        let inputName = input.getAttribute(\"name\");\n        let inputType = input.getAttribute(\"type\");\n        if (inputType) inputType = inputType.toLowerCase();\n\n        // If the input doesn't have a name, we can't use it.\n        if (typeof inputName === \"undefined\" || inputName === null) continue;\n\n        if (input.tagName === \"SELECT\" && input.hasAttribute(\"multiple\")) {\n          // Possibly multiple values\n          for (let option of input.options) {\n            if (option.selected) {\n              formData.append(inputName, option.value);\n            }\n          }\n        } else if (\n          !inputType ||\n          (inputType !== \"checkbox\" && inputType !== \"radio\") ||\n          input.checked\n        ) {\n          formData.append(inputName, input.value);\n        }\n      }\n    }\n  }\n\n  // Invoked when there is new progress information about given files.\n  // If e is not provided, it is assumed that the upload is finished.\n  _updateFilesUploadProgress(files, xhr, e) {\n    if (!files[0].upload.chunked) {\n      // Handle file uploads without chunking\n      for (let file of files) {\n        if (\n          file.upload.total &&\n          file.upload.bytesSent &&\n          file.upload.bytesSent == file.upload.total\n        ) {\n          // If both, the `total` and `bytesSent` have already been set, and\n          // they are equal (meaning progress is at 100%), we can skip this\n          // file, since an upload progress shouldn't go down.\n          continue;\n        }\n\n        if (e) {\n          file.upload.progress = (100 * e.loaded) / e.total;\n          file.upload.total = e.total;\n          file.upload.bytesSent = e.loaded;\n        } else {\n          // No event, so we're at 100%\n          file.upload.progress = 100;\n          file.upload.bytesSent = file.upload.total;\n        }\n\n        this.emit(\n          \"uploadprogress\",\n          file,\n          file.upload.progress,\n          file.upload.bytesSent\n        );\n      }\n    } else {\n      // Handle chunked file uploads\n\n      // Chunked upload is not compatible with uploading multiple files in one\n      // request, so we know there's only one file.\n      let file = files[0];\n\n      // Since this is a chunked upload, we need to update the appropriate chunk\n      // progress.\n      let chunk = this._getChunk(file, xhr);\n\n      if (e) {\n        chunk.progress = (100 * e.loaded) / e.total;\n        chunk.total = e.total;\n        chunk.bytesSent = e.loaded;\n      } else {\n        // No event, so we're at 100%\n        chunk.progress = 100;\n        chunk.bytesSent = chunk.total;\n      }\n\n      // Now tally the *file* upload progress from its individual chunks\n      file.upload.progress = 0;\n      file.upload.total = 0;\n      file.upload.bytesSent = 0;\n      for (let i = 0; i < file.upload.totalChunkCount; i++) {\n        if (\n          file.upload.chunks[i] &&\n          typeof file.upload.chunks[i].progress !== \"undefined\"\n        ) {\n          file.upload.progress += file.upload.chunks[i].progress;\n          file.upload.total += file.upload.chunks[i].total;\n          file.upload.bytesSent += file.upload.chunks[i].bytesSent;\n        }\n      }\n      // Since the process is a percentage, we need to divide by the amount of\n      // chunks we've used.\n      file.upload.progress = file.upload.progress / file.upload.totalChunkCount;\n\n      this.emit(\n        \"uploadprogress\",\n        file,\n        file.upload.progress,\n        file.upload.bytesSent\n      );\n    }\n  }\n\n  _finishedUploading(files, xhr, e) {\n    let response;\n\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (xhr.readyState !== 4) {\n      return;\n    }\n\n    if (xhr.responseType !== \"arraybuffer\" && xhr.responseType !== \"blob\") {\n      response = xhr.responseText;\n\n      if (\n        xhr.getResponseHeader(\"content-type\") &&\n        ~xhr.getResponseHeader(\"content-type\").indexOf(\"application/json\")\n      ) {\n        try {\n          response = JSON.parse(response);\n        } catch (error) {\n          e = error;\n          response = \"Invalid JSON response from server.\";\n        }\n      }\n    }\n\n    this._updateFilesUploadProgress(files, xhr);\n\n    if (!(200 <= xhr.status && xhr.status < 300)) {\n      this._handleUploadError(files, xhr, response);\n    } else {\n      if (files[0].upload.chunked) {\n        files[0].upload.finishedChunkUpload(\n          this._getChunk(files[0], xhr),\n          response\n        );\n      } else {\n        this._finished(files, response, e);\n      }\n    }\n  }\n\n  _handleUploadError(files, xhr, response) {\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (files[0].upload.chunked && this.options.retryChunks) {\n      let chunk = this._getChunk(files[0], xhr);\n      if (chunk.retries++ < this.options.retryChunksLimit) {\n        this._uploadData(files, [chunk.dataBlock]);\n        return;\n      } else {\n        console.warn(\"Retried this chunk too often. Giving up.\");\n      }\n    }\n\n    this._errorProcessing(\n      files,\n      response ||\n        this.options.dictResponseError.replace(\"{{statusCode}}\", xhr.status),\n      xhr\n    );\n  }\n\n  submitRequest(xhr, formData, files) {\n    if (xhr.readyState != 1) {\n      console.warn(\n        \"Cannot send this request because the XMLHttpRequest.readyState is not OPENED.\"\n      );\n      return;\n    }\n    if (this.options.binaryBody) {\n      if (files[0].upload.chunked) {\n        const chunk = this._getChunk(files[0], xhr);\n        xhr.send(chunk.dataBlock.data);\n      } else {\n        xhr.send(files[0]);\n      }\n    } else {\n      xhr.send(formData);\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _finished(files, responseText, e) {\n    for (let file of files) {\n      file.status = Dropzone.SUCCESS;\n      this.emit(\"success\", file, responseText, e);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"successmultiple\", files, responseText, e);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _errorProcessing(files, message, xhr) {\n    for (let file of files) {\n      file.status = Dropzone.ERROR;\n      this.emit(\"error\", file, message, xhr);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"errormultiple\", files, message, xhr);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  static uuidv4() {\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(\n      /[xy]/g,\n      function (c) {\n        let r = (Math.random() * 16) | 0,\n          v = c === \"x\" ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n      }\n    );\n  }\n}\nDropzone.initClass();\n\n// This is a map of options for your different dropzones. Add configurations\n// to this object for your different dropzone elemens.\n//\n// Example:\n//\n//     Dropzone.options.myDropzoneElementId = { maxFilesize: 1 };\n//\n// And in html:\n//\n//     <form action=\"/upload\" id=\"my-dropzone-element-id\" class=\"dropzone\"></form>\nDropzone.options = {};\n\n// Returns the options for an element or undefined if none available.\nDropzone.optionsForElement = function (element) {\n  // Get the `Dropzone.options.elementId` for this element if it exists\n  if (element.getAttribute(\"id\")) {\n    return Dropzone.options[camelize(element.getAttribute(\"id\"))];\n  } else {\n    return undefined;\n  }\n};\n\n// Holds a list of all dropzone instances\nDropzone.instances = [];\n\n// Returns the dropzone for given element if any\nDropzone.forElement = function (element) {\n  if (typeof element === \"string\") {\n    element = document.querySelector(element);\n  }\n  if ((element != null ? element.dropzone : undefined) == null) {\n    throw new Error(\n      \"No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.\"\n    );\n  }\n  return element.dropzone;\n};\n\n// Looks for all .dropzone elements and creates a dropzone for them\nDropzone.discover = function () {\n  let dropzones;\n  if (document.querySelectorAll) {\n    dropzones = document.querySelectorAll(\".dropzone\");\n  } else {\n    dropzones = [];\n    // IE :(\n    let checkElements = (elements) =>\n      (() => {\n        let result = [];\n        for (let el of elements) {\n          if (/(^| )dropzone($| )/.test(el.className)) {\n            result.push(dropzones.push(el));\n          } else {\n            result.push(undefined);\n          }\n        }\n        return result;\n      })();\n    checkElements(document.getElementsByTagName(\"div\"));\n    checkElements(document.getElementsByTagName(\"form\"));\n  }\n\n  return (() => {\n    let result = [];\n    for (let dropzone of dropzones) {\n      // Create a dropzone unless auto discover has been disabled for specific element\n      if (Dropzone.optionsForElement(dropzone) !== false) {\n        result.push(new Dropzone(dropzone));\n      } else {\n        result.push(undefined);\n      }\n    }\n    return result;\n  })();\n};\n\n// Some browsers support drag and drog functionality, but not correctly.\n//\n// So I created a blocklist of userAgents. Yes, yes. Browser sniffing, I know.\n// But what to do when browsers *theoretically* support an API, but crash\n// when using it.\n//\n// This is a list of regular expressions tested against navigator.userAgent\n//\n// ** It should only be used on browser that *do* support the API, but\n// incorrectly **\nDropzone.blockedBrowsers = [\n  // The mac os and windows phone version of opera 12 seems to have a problem with the File drag'n'drop API.\n  /opera.*(Macintosh|Windows Phone).*version\\/12/i,\n];\n\n// Checks if the browser is supported\nDropzone.isBrowserSupported = function () {\n  let capableBrowser = true;\n\n  if (\n    window.File &&\n    window.FileReader &&\n    window.FileList &&\n    window.Blob &&\n    window.FormData &&\n    document.querySelector\n  ) {\n    if (!(\"classList\" in document.createElement(\"a\"))) {\n      capableBrowser = false;\n    } else {\n      if (Dropzone.blacklistedBrowsers !== undefined) {\n        // Since this has been renamed, this makes sure we don't break older\n        // configuration.\n        Dropzone.blockedBrowsers = Dropzone.blacklistedBrowsers;\n      }\n      // The browser supports the API, but may be blocked.\n      for (let regex of Dropzone.blockedBrowsers) {\n        if (regex.test(navigator.userAgent)) {\n          capableBrowser = false;\n          continue;\n        }\n      }\n    }\n  } else {\n    capableBrowser = false;\n  }\n\n  return capableBrowser;\n};\n\nDropzone.dataURItoBlob = function (dataURI) {\n  // convert base64 to raw binary data held in a string\n  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this\n  let byteString = atob(dataURI.split(\",\")[1]);\n\n  // separate out the mime component\n  let mimeString = dataURI.split(\",\")[0].split(\":\")[1].split(\";\")[0];\n\n  // write the bytes of the string to an ArrayBuffer\n  let ab = new ArrayBuffer(byteString.length);\n  let ia = new Uint8Array(ab);\n  for (\n    let i = 0, end = byteString.length, asc = 0 <= end;\n    asc ? i <= end : i >= end;\n    asc ? i++ : i--\n  ) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n\n  // write the ArrayBuffer to a blob\n  return new Blob([ab], { type: mimeString });\n};\n\n// Returns an array without the rejected item\nconst without = (list, rejectedItem) =>\n  list.filter((item) => item !== rejectedItem).map((item) => item);\n\n// abc-def_ghi -> abcDefGhi\nconst camelize = (str) =>\n  str.replace(/[\\-_](\\w)/g, (match) => match.charAt(1).toUpperCase());\n\n// Creates an element from string\nDropzone.createElement = function (string) {\n  let div = document.createElement(\"div\");\n  div.innerHTML = string;\n  return div.childNodes[0];\n};\n\n// Tests if given element is inside (or simply is) the container\nDropzone.elementInside = function (element, container) {\n  if (element === container) {\n    return true;\n  } // Coffeescript doesn't support do/while loops\n  while ((element = element.parentNode)) {\n    if (element === container) {\n      return true;\n    }\n  }\n  return false;\n};\n\nDropzone.getElement = function (el, name) {\n  let element;\n  if (typeof el === \"string\") {\n    element = document.querySelector(el);\n  } else if (el.nodeType != null) {\n    element = el;\n  }\n  if (element == null) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector or a plain HTML element.`\n    );\n  }\n  return element;\n};\n\nDropzone.getElements = function (els, name) {\n  let el, elements;\n  if (els instanceof Array) {\n    elements = [];\n    try {\n      for (el of els) {\n        elements.push(this.getElement(el, name));\n      }\n    } catch (e) {\n      elements = null;\n    }\n  } else if (typeof els === \"string\") {\n    elements = [];\n    for (el of document.querySelectorAll(els)) {\n      elements.push(el);\n    }\n  } else if (els.nodeType != null) {\n    elements = [els];\n  }\n\n  if (elements == null || !elements.length) {\n    throw new Error(\n      `Invalid \\`${name}\\` option provided. Please provide a CSS selector, a plain HTML element or a list of those.`\n    );\n  }\n\n  return elements;\n};\n\n// Asks the user the question and calls accepted or rejected accordingly\n//\n// The default implementation just uses `window.confirm` and then calls the\n// appropriate callback.\nDropzone.confirm = function (question, accepted, rejected) {\n  if (window.confirm(question)) {\n    return accepted();\n  } else if (rejected != null) {\n    return rejected();\n  }\n};\n\n// Validates the mime type like this:\n//\n// https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept\nDropzone.isValidFile = function (file, acceptedFiles) {\n  if (!acceptedFiles) {\n    return true;\n  } // If there are no accepted mime types, it's OK\n  acceptedFiles = acceptedFiles.split(\",\");\n\n  let mimeType = file.type;\n  let baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n\n  for (let validType of acceptedFiles) {\n    validType = validType.trim();\n    if (validType.charAt(0) === \".\") {\n      if (\n        file.name\n          .toLowerCase()\n          .indexOf(\n            validType.toLowerCase(),\n            file.name.length - validType.length\n          ) !== -1\n      ) {\n        return true;\n      }\n    } else if (/\\/\\*$/.test(validType)) {\n      // This is something like a image/* mime type\n      if (baseMimeType === validType.replace(/\\/.*$/, \"\")) {\n        return true;\n      }\n    } else {\n      if (mimeType === validType) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n\n// Augment jQuery\nif (typeof jQuery !== \"undefined\" && jQuery !== null) {\n  jQuery.fn.dropzone = function (options) {\n    return this.each(function () {\n      return new Dropzone(this, options);\n    });\n  };\n}\n\n// Dropzone file status codes\nDropzone.ADDED = \"added\";\n\nDropzone.QUEUED = \"queued\";\n// For backwards compatibility. Now, if a file is accepted, it's either queued\n// or uploading.\nDropzone.ACCEPTED = Dropzone.QUEUED;\n\nDropzone.UPLOADING = \"uploading\";\nDropzone.PROCESSING = Dropzone.UPLOADING; // alias\n\nDropzone.CANCELED = \"canceled\";\nDropzone.ERROR = \"error\";\nDropzone.SUCCESS = \"success\";\n\n/*\n\n Bugfix for iOS 6 and 7\n Source: http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios\n based on the work of https://github.com/stomita/ios-imagefile-megapixel\n\n */\n\n// Detecting vertical squash in loaded image.\n// Fixes a bug which squash image vertically while drawing into canvas for some images.\n// This is a bug in iOS6 devices. This function from https://github.com/stomita/ios-imagefile-megapixel\nlet detectVerticalSquash = function (img) {\n  let iw = img.naturalWidth;\n  let ih = img.naturalHeight;\n  let canvas = document.createElement(\"canvas\");\n  canvas.width = 1;\n  canvas.height = ih;\n  let ctx = canvas.getContext(\"2d\");\n  ctx.drawImage(img, 0, 0);\n  let { data } = ctx.getImageData(1, 0, 1, ih);\n\n  // search image edge pixel position in case it is squashed vertically.\n  let sy = 0;\n  let ey = ih;\n  let py = ih;\n  while (py > sy) {\n    let alpha = data[(py - 1) * 4 + 3];\n\n    if (alpha === 0) {\n      ey = py;\n    } else {\n      sy = py;\n    }\n\n    py = (ey + sy) >> 1;\n  }\n  let ratio = py / ih;\n\n  if (ratio === 0) {\n    return 1;\n  } else {\n    return ratio;\n  }\n};\n\n// A replacement for context.drawImage\n// (args are for source and destination).\nvar drawImageIOSFix = function (ctx, img, sx, sy, sw, sh, dx, dy, dw, dh) {\n  let vertSquashRatio = detectVerticalSquash(img);\n  return ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh / vertSquashRatio);\n};\n\n// Based on MinifyJpeg\n// Source: http://www.perry.cz/files/ExifRestorer.js\n// http://elicon.blog57.fc2.com/blog-entry-206.html\nclass ExifRestore {\n  static initClass() {\n    this.KEY_STR =\n      \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n  }\n\n  static encode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    while (true) {\n      chr1 = input[i++];\n      chr2 = input[i++];\n      chr3 = input[i++];\n      enc1 = chr1 >> 2;\n      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n      enc4 = chr3 & 63;\n      if (isNaN(chr2)) {\n        enc3 = enc4 = 64;\n      } else if (isNaN(chr3)) {\n        enc4 = 64;\n      }\n      output =\n        output +\n        this.KEY_STR.charAt(enc1) +\n        this.KEY_STR.charAt(enc2) +\n        this.KEY_STR.charAt(enc3) +\n        this.KEY_STR.charAt(enc4);\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return output;\n  }\n\n  static restore(origFileBase64, resizedFileBase64) {\n    if (!origFileBase64.match(\"data:image/jpeg;base64,\")) {\n      return resizedFileBase64;\n    }\n    let rawImage = this.decode64(\n      origFileBase64.replace(\"data:image/jpeg;base64,\", \"\")\n    );\n    let segments = this.slice2Segments(rawImage);\n    let image = this.exifManipulation(resizedFileBase64, segments);\n    return `data:image/jpeg;base64,${this.encode64(image)}`;\n  }\n\n  static exifManipulation(resizedFileBase64, segments) {\n    let exifArray = this.getExifArray(segments);\n    let newImageArray = this.insertExif(resizedFileBase64, exifArray);\n    let aBuffer = new Uint8Array(newImageArray);\n    return aBuffer;\n  }\n\n  static getExifArray(segments) {\n    let seg = undefined;\n    let x = 0;\n    while (x < segments.length) {\n      seg = segments[x];\n      if ((seg[0] === 255) & (seg[1] === 225)) {\n        return seg;\n      }\n      x++;\n    }\n    return [];\n  }\n\n  static insertExif(resizedFileBase64, exifArray) {\n    let imageData = resizedFileBase64.replace(\"data:image/jpeg;base64,\", \"\");\n    let buf = this.decode64(imageData);\n    let separatePoint = buf.indexOf(255, 3);\n    let mae = buf.slice(0, separatePoint);\n    let ato = buf.slice(separatePoint);\n    let array = mae;\n    array = array.concat(exifArray);\n    array = array.concat(ato);\n    return array;\n  }\n\n  static slice2Segments(rawImageArray) {\n    let head = 0;\n    let segments = [];\n    while (true) {\n      var length;\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 218)) {\n        break;\n      }\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 216)) {\n        head += 2;\n      } else {\n        length = rawImageArray[head + 2] * 256 + rawImageArray[head + 3];\n        let endPoint = head + length + 2;\n        let seg = rawImageArray.slice(head, endPoint);\n        segments.push(seg);\n        head = endPoint;\n      }\n      if (head > rawImageArray.length) {\n        break;\n      }\n    }\n    return segments;\n  }\n\n  static decode64(input) {\n    let output = \"\";\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = \"\";\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = \"\";\n    let i = 0;\n    let buf = [];\n    // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n    let base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n    if (base64test.exec(input)) {\n      console.warn(\n        \"There were invalid base64 characters in the input text.\\nValid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\\nExpect errors in decoding.\"\n      );\n    }\n    input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, \"\");\n    while (true) {\n      enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n      chr1 = (enc1 << 2) | (enc2 >> 4);\n      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n      chr3 = ((enc3 & 3) << 6) | enc4;\n      buf.push(chr1);\n      if (enc3 !== 64) {\n        buf.push(chr2);\n      }\n      if (enc4 !== 64) {\n        buf.push(chr3);\n      }\n      chr1 = chr2 = chr3 = \"\";\n      enc1 = enc2 = enc3 = enc4 = \"\";\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return buf;\n  }\n}\nExifRestore.initClass();\n\n/*\n * contentloaded.js\n *\n * Author: Diego Perini (diego.perini at gmail.com)\n * Summary: cross-browser wrapper for DOMContentLoaded\n * Updated: 20101020\n * License: MIT\n * Version: 1.2\n *\n * URL:\n * http://javascript.nwbox.com/ContentLoaded/\n * http://javascript.nwbox.com/ContentLoaded/MIT-LICENSE\n */\n\n// @win window reference\n// @fn function reference\nlet contentLoaded = function (win, fn) {\n  let done = false;\n  let top = true;\n  let doc = win.document;\n  let root = doc.documentElement;\n  let add = doc.addEventListener ? \"addEventListener\" : \"attachEvent\";\n  let rem = doc.addEventListener ? \"removeEventListener\" : \"detachEvent\";\n  let pre = doc.addEventListener ? \"\" : \"on\";\n  var init = function (e) {\n    if (e.type === \"readystatechange\" && doc.readyState !== \"complete\") {\n      return;\n    }\n    (e.type === \"load\" ? win : doc)[rem](pre + e.type, init, false);\n    if (!done && (done = true)) {\n      return fn.call(win, e.type || e);\n    }\n  };\n\n  var poll = function () {\n    try {\n      root.doScroll(\"left\");\n    } catch (e) {\n      setTimeout(poll, 50);\n      return;\n    }\n    return init(\"poll\");\n  };\n\n  if (doc.readyState !== \"complete\") {\n    if (doc.createEventObject && root.doScroll) {\n      try {\n        top = !win.frameElement;\n      } catch (error) {}\n      if (top) {\n        poll();\n      }\n    }\n    doc[add](pre + \"DOMContentLoaded\", init, false);\n    doc[add](pre + \"readystatechange\", init, false);\n    return win[add](pre + \"load\", init, false);\n  }\n};\n\nfunction __guard__(value, transform) {\n  return typeof value !== \"undefined\" && value !== null\n    ? transform(value)\n    : undefined;\n}\nfunction __guardMethod__(obj, methodName, transform) {\n  if (\n    typeof obj !== \"undefined\" &&\n    obj !== null &&\n    typeof obj[methodName] === \"function\"\n  ) {\n    return transform(obj, methodName);\n  } else {\n    return undefined;\n  }\n}\n\nexport { Dropzone };\n", "// The Emitter class provides the ability to call `.on()` on Dropzone to listen\n// to events.\n// It is strongly based on component's emitter class, and I removed the\n// functionality because of the dependency hell with different frameworks.\nexport default class Emitter {\n  // Add an event listener for given event\n  on(event, fn) {\n    this._callbacks = this._callbacks || {};\n    // Create namespace for this event\n    if (!this._callbacks[event]) {\n      this._callbacks[event] = [];\n    }\n    this._callbacks[event].push(fn);\n    return this;\n  }\n\n  emit(event, ...args) {\n    this._callbacks = this._callbacks || {};\n    let callbacks = this._callbacks[event];\n\n    if (callbacks) {\n      for (let callback of callbacks) {\n        callback.apply(this, args);\n      }\n    }\n    // trigger a corresponding DOM event\n    if (this.element) {\n      this.element.dispatchEvent(\n        this.makeEvent(\"dropzone:\" + event, { args: args })\n      );\n    }\n    return this;\n  }\n\n  makeEvent(eventName, detail) {\n    let params = { bubbles: true, cancelable: true, detail: detail };\n\n    if (typeof window.CustomEvent === \"function\") {\n      return new CustomEvent(eventName, params);\n    } else {\n      // IE 11 support\n      // https://developer.mozilla.org/en-US/docs/Web/API/CustomEvent/CustomEvent\n      var evt = document.createEvent(\"CustomEvent\");\n      evt.initCustomEvent(\n        eventName,\n        params.bubbles,\n        params.cancelable,\n        params.detail\n      );\n      return evt;\n    }\n  }\n\n  // Remove event listener for given event. If fn is not provided, all event\n  // listeners for that event will be removed. If neither is provided, all\n  // event listeners will be removed.\n  off(event, fn) {\n    if (!this._callbacks || arguments.length === 0) {\n      this._callbacks = {};\n      return this;\n    }\n\n    // specific event\n    let callbacks = this._callbacks[event];\n    if (!callbacks) {\n      return this;\n    }\n\n    // remove all handlers\n    if (arguments.length === 1) {\n      delete this._callbacks[event];\n      return this;\n    }\n\n    // remove specific handler\n    for (let i = 0; i < callbacks.length; i++) {\n      let callback = callbacks[i];\n      if (callback === fn) {\n        callbacks.splice(i, 1);\n        break;\n      }\n    }\n\n    return this;\n  }\n}\n", "import Dropzone from \"./dropzone\";\nimport defaultPreviewTemplate from \"bundle-text:./preview-template.html\";\n\nlet defaultOptions = {\n  /**\n   * Has to be specified on elements other than form (or when the form doesn't\n   * have an `action` attribute).\n   *\n   * You can also provide a function that will be called with `files` and\n   * `dataBlocks`  and must return the url as string.\n   */\n  url: null,\n\n  /**\n   * Can be changed to `\"put\"` if necessary. You can also provide a function\n   * that will be called with `files` and must return the method (since `v3.12.0`).\n   */\n  method: \"post\",\n\n  /**\n   * Will be set on the XHRequest.\n   */\n  withCredentials: false,\n\n  /**\n   * The timeout for the XHR requests in milliseconds (since `v4.4.0`).\n   * If set to null or 0, no timeout is going to be set.\n   */\n  timeout: null,\n\n  /**\n   * How many file uploads to process in parallel (See the\n   * Enqueuing file uploads documentation section for more info)\n   */\n  parallelUploads: 2,\n\n  /**\n   * Whether to send multiple files in one request. If\n   * this it set to true, then the fallback file input element will\n   * have the `multiple` attribute as well. This option will\n   * also trigger additional events (like `processingmultiple`). See the events\n   * documentation section for more information.\n   */\n  uploadMultiple: false,\n\n  /**\n   * Whether you want files to be uploaded in chunks to your server. This can't be\n   * used in combination with `uploadMultiple`.\n   *\n   * See [chunksUploaded](#config-chunksUploaded) for the callback to finalise an upload.\n   */\n  chunking: false,\n\n  /**\n   * If `chunking` is enabled, this defines whether **every** file should be chunked,\n   * even if the file size is below chunkSize. This means, that the additional chunk\n   * form data will be submitted and the `chunksUploaded` callback will be invoked.\n   */\n  forceChunking: false,\n\n  /**\n   * If `chunking` is `true`, then this defines the chunk size in bytes.\n   */\n  chunkSize: 2 * 1024 * 1024,\n\n  /**\n   * If `true`, the individual chunks of a file are being uploaded simultaneously.\n   */\n  parallelChunkUploads: false,\n\n  /**\n   * Whether a chunk should be retried if it fails.\n   */\n  retryChunks: false,\n\n  /**\n   * If `retryChunks` is true, how many times should it be retried.\n   */\n  retryChunksLimit: 3,\n\n  /**\n   * The maximum filesize (in MiB) that is allowed to be uploaded.\n   */\n  maxFilesize: 256,\n\n  /**\n   * The name of the file param that gets transferred.\n   * **NOTE**: If you have the option  `uploadMultiple` set to `true`, then\n   * Dropzone will append `[]` to the name.\n   */\n  paramName: \"file\",\n\n  /**\n   * Whether thumbnails for images should be generated\n   */\n  createImageThumbnails: true,\n\n  /**\n   * In MB. When the filename exceeds this limit, the thumbnail will not be generated.\n   */\n  maxThumbnailFilesize: 10,\n\n  /**\n   * If `null`, the ratio of the image will be used to calculate it.\n   */\n  thumbnailWidth: 120,\n\n  /**\n   * The same as `thumbnailWidth`. If both are null, images will not be resized.\n   */\n  thumbnailHeight: 120,\n\n  /**\n   * How the images should be scaled down in case both, `thumbnailWidth` and `thumbnailHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  thumbnailMethod: \"crop\",\n\n  /**\n   * If set, images will be resized to these dimensions before being **uploaded**.\n   * If only one, `resizeWidth` **or** `resizeHeight` is provided, the original aspect\n   * ratio of the file will be preserved.\n   *\n   * The `options.transformFile` function uses these options, so if the `transformFile` function\n   * is overridden, these options don't do anything.\n   */\n  resizeWidth: null,\n\n  /**\n   * See `resizeWidth`.\n   */\n  resizeHeight: null,\n\n  /**\n   * The mime type of the resized image (before it gets uploaded to the server).\n   * If `null` the original mime type will be used. To force jpeg, for example, use `image/jpeg`.\n   * See `resizeWidth` for more information.\n   */\n  resizeMimeType: null,\n\n  /**\n   * The quality of the resized images. See `resizeWidth`.\n   */\n  resizeQuality: 0.8,\n\n  /**\n   * How the images should be scaled down in case both, `resizeWidth` and `resizeHeight` are provided.\n   * Can be either `contain` or `crop`.\n   */\n  resizeMethod: \"contain\",\n\n  /**\n   * The base that is used to calculate the **displayed** filesize. You can\n   * change this to 1024 if you would rather display kibibytes, mebibytes,\n   * etc... 1024 is technically incorrect, because `1024 bytes` are `1 kibibyte`\n   * not `1 kilobyte`. You can change this to `1024` if you don't care about\n   * validity.\n   */\n  filesizeBase: 1000,\n\n  /**\n   * If not `null` defines how many files this Dropzone handles. If it exceeds,\n   * the event `maxfilesexceeded` will be called. The dropzone element gets the\n   * class `dz-max-files-reached` accordingly so you can provide visual\n   * feedback.\n   */\n  maxFiles: null,\n\n  /**\n   * An optional object to send additional headers to the server. Eg:\n   * `{ \"My-Awesome-Header\": \"header value\" }`\n   */\n  headers: null,\n\n  /**\n   * Should the default headers be set or not?\n   * Accept: application/json <- for requesting json response\n   * Cache-Control: no-cache <- Request shouldnt be cached\n   * X-Requested-With: XMLHttpRequest <- We sent the request via XMLHttpRequest\n   */\n  defaultHeaders: true,\n\n  /**\n   * If `true`, the dropzone element itself will be clickable, if `false`\n   * nothing will be clickable.\n   *\n   * You can also pass an HTML element, a CSS selector (for multiple elements)\n   * or an array of those. In that case, all of those elements will trigger an\n   * upload when clicked.\n   */\n  clickable: true,\n\n  /**\n   * Whether hidden files in directories should be ignored.\n   */\n  ignoreHiddenFiles: true,\n\n  /**\n   * The default implementation of `accept` checks the file's mime type or\n   * extension against this list. This is a comma separated list of mime\n   * types or file extensions.\n   *\n   * Eg.: `image/*,application/pdf,.psd`\n   *\n   * If the Dropzone is `clickable` this option will also be used as\n   * [`accept`](https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept)\n   * parameter on the hidden file input as well.\n   */\n  acceptedFiles: null,\n\n  /**\n   * **Deprecated!**\n   * Use acceptedFiles instead.\n   */\n  acceptedMimeTypes: null,\n\n  /**\n   * If false, files will be added to the queue but the queue will not be\n   * processed automatically.\n   * This can be useful if you need some additional user input before sending\n   * files (or if you want want all files sent at once).\n   * If you're ready to send the file simply call `myDropzone.processQueue()`.\n   *\n   * See the [enqueuing file uploads](#enqueuing-file-uploads) documentation\n   * section for more information.\n   */\n  autoProcessQueue: true,\n\n  /**\n   * If false, files added to the dropzone will not be queued by default.\n   * You'll have to call `enqueueFile(file)` manually.\n   */\n  autoQueue: true,\n\n  /**\n   * If `true`, this will add a link to every file preview to remove or cancel (if\n   * already uploading) the file. The `dictCancelUpload`, `dictCancelUploadConfirmation`\n   * and `dictRemoveFile` options are used for the wording.\n   */\n  addRemoveLinks: false,\n\n  /**\n   * Defines where to display the file previews – if `null` the\n   * Dropzone element itself is used. Can be a plain `HTMLElement` or a CSS\n   * selector. The element should have the `dropzone-previews` class so\n   * the previews are displayed properly.\n   */\n  previewsContainer: null,\n\n  /**\n   * Set this to `true` if you don't want previews to be shown.\n   */\n  disablePreviews: false,\n\n  /**\n   * This is the element the hidden input field (which is used when clicking on the\n   * dropzone to trigger file selection) will be appended to. This might\n   * be important in case you use frameworks to switch the content of your page.\n   *\n   * Can be a selector string, or an element directly.\n   */\n  hiddenInputContainer: \"body\",\n\n  /**\n   * If null, no capture type will be specified\n   * If camera, mobile devices will skip the file selection and choose camera\n   * If microphone, mobile devices will skip the file selection and choose the microphone\n   * If camcorder, mobile devices will skip the file selection and choose the camera in video mode\n   * On apple devices multiple must be set to false.  AcceptedFiles may need to\n   * be set to an appropriate mime type (e.g. \"image/*\", \"audio/*\", or \"video/*\").\n   */\n  capture: null,\n\n  /**\n   * **Deprecated**. Use `renameFile` instead.\n   */\n  renameFilename: null,\n\n  /**\n   * A function that is invoked before the file is uploaded to the server and renames the file.\n   * This function gets the `File` as argument and can use the `file.name`. The actual name of the\n   * file that gets used during the upload can be accessed through `file.upload.filename`.\n   */\n  renameFile: null,\n\n  /**\n   * If `true` the fallback will be forced. This is very useful to test your server\n   * implementations first and make sure that everything works as\n   * expected without dropzone if you experience problems, and to test\n   * how your fallbacks will look.\n   */\n  forceFallback: false,\n\n  /**\n   * The text used before any files are dropped.\n   */\n  dictDefaultMessage: \"Drop files here to upload\",\n\n  /**\n   * The text that replaces the default message text it the browser is not supported.\n   */\n  dictFallbackMessage:\n    \"Your browser does not support drag'n'drop file uploads.\",\n\n  /**\n   * The text that will be added before the fallback form.\n   * If you provide a  fallback element yourself, or if this option is `null` this will\n   * be ignored.\n   */\n  dictFallbackText:\n    \"Please use the fallback form below to upload your files like in the olden days.\",\n\n  /**\n   * If the filesize is too big.\n   * `{{filesize}}` and `{{maxFilesize}}` will be replaced with the respective configuration values.\n   */\n  dictFileTooBig:\n    \"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.\",\n\n  /**\n   * If the file doesn't match the file type.\n   */\n  dictInvalidFileType: \"You can't upload files of this type.\",\n\n  /**\n   * If the server response was invalid.\n   * `{{statusCode}}` will be replaced with the servers status code.\n   */\n  dictResponseError: \"Server responded with {{statusCode}} code.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for the cancel upload link.\n   */\n  dictCancelUpload: \"Cancel upload\",\n\n  /**\n   * The text that is displayed if an upload was manually canceled\n   */\n  dictUploadCanceled: \"Upload canceled.\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used for confirmation when cancelling upload.\n   */\n  dictCancelUploadConfirmation: \"Are you sure you want to cancel this upload?\",\n\n  /**\n   * If `addRemoveLinks` is true, the text to be used to remove a file.\n   */\n  dictRemoveFile: \"Remove file\",\n\n  /**\n   * If this is not null, then the user will be prompted before removing a file.\n   */\n  dictRemoveFileConfirmation: null,\n\n  /**\n   * Displayed if `maxFiles` is st and exceeded.\n   * The string `{{maxFiles}}` will be replaced by the configuration value.\n   */\n  dictMaxFilesExceeded: \"You can not upload any more files.\",\n\n  /**\n   * Allows you to translate the different units. Starting with `tb` for terabytes and going down to\n   * `b` for bytes.\n   */\n  dictFileSizeUnits: { tb: \"TB\", gb: \"GB\", mb: \"MB\", kb: \"KB\", b: \"b\" },\n  /**\n   * Called when dropzone initialized\n   * You can add event listeners here\n   */\n  init() {},\n\n  /**\n   * Can be an **object** of additional parameters to transfer to the server, **or** a `Function`\n   * that gets invoked with the `files`, `xhr` and, if it's a chunked upload, `chunk` arguments. In case\n   * of a function, this needs to return a map.\n   *\n   * The default implementation does nothing for normal uploads, but adds relevant information for\n   * chunked uploads.\n   *\n   * This is the same as adding hidden input fields in the form element.\n   */\n  params(files, xhr, chunk) {\n    if (chunk) {\n      return {\n        dzuuid: chunk.file.upload.uuid,\n        dzchunkindex: chunk.index,\n        dztotalfilesize: chunk.file.size,\n        dzchunksize: this.options.chunkSize,\n        dztotalchunkcount: chunk.file.upload.totalChunkCount,\n        dzchunkbyteoffset: chunk.index * this.options.chunkSize,\n      };\n    }\n  },\n\n  /**\n   * A function that gets a [file](https://developer.mozilla.org/en-US/docs/DOM/File)\n   * and a `done` function as parameters.\n   *\n   * If the done function is invoked without arguments, the file is \"accepted\" and will\n   * be processed. If you pass an error message, the file is rejected, and the error\n   * message will be displayed.\n   * This function will not be called if the file is too big or doesn't match the mime types.\n   */\n  accept(file, done) {\n    return done();\n  },\n\n  /**\n   * The callback that will be invoked when all chunks have been uploaded for a file.\n   * It gets the file for which the chunks have been uploaded as the first parameter,\n   * and the `done` function as second. `done()` needs to be invoked when everything\n   * needed to finish the upload process is done.\n   */\n  chunksUploaded: function (file, done) {\n    done();\n  },\n\n  /**\n   * Sends the file as binary blob in body instead of form data.\n   * If this is set, the `params` option will be ignored.\n   * It's an error to set this to `true` along with `uploadMultiple` since\n   * multiple files cannot be in a single binary body.\n   */\n  binaryBody: false,\n\n  /**\n   * Gets called when the browser is not supported.\n   * The default implementation shows the fallback input field and adds\n   * a text.\n   */\n  fallback() {\n    // This code should pass in IE7... :(\n    let messageElement;\n    this.element.className = `${this.element.className} dz-browser-not-supported`;\n\n    for (let child of this.element.getElementsByTagName(\"div\")) {\n      if (/(^| )dz-message($| )/.test(child.className)) {\n        messageElement = child;\n        child.className = \"dz-message\"; // Removes the 'dz-default' class\n        break;\n      }\n    }\n    if (!messageElement) {\n      messageElement = Dropzone.createElement(\n        '<div class=\"dz-message\"><span></span></div>'\n      );\n      this.element.appendChild(messageElement);\n    }\n\n    let span = messageElement.getElementsByTagName(\"span\")[0];\n    if (span) {\n      if (span.textContent != null) {\n        span.textContent = this.options.dictFallbackMessage;\n      } else if (span.innerText != null) {\n        span.innerText = this.options.dictFallbackMessage;\n      }\n    }\n\n    return this.element.appendChild(this.getFallbackForm());\n  },\n\n  /**\n   * Gets called to calculate the thumbnail dimensions.\n   *\n   * It gets `file`, `width` and `height` (both may be `null`) as parameters and must return an object containing:\n   *\n   *  - `srcWidth` & `srcHeight` (required)\n   *  - `trgWidth` & `trgHeight` (required)\n   *  - `srcX` & `srcY` (optional, default `0`)\n   *  - `trgX` & `trgY` (optional, default `0`)\n   *\n   * Those values are going to be used by `ctx.drawImage()`.\n   */\n  resize(file, width, height, resizeMethod) {\n    let info = {\n      srcX: 0,\n      srcY: 0,\n      srcWidth: file.width,\n      srcHeight: file.height,\n    };\n\n    let srcRatio = file.width / file.height;\n\n    // Automatically calculate dimensions if not specified\n    if (width == null && height == null) {\n      width = info.srcWidth;\n      height = info.srcHeight;\n    } else if (width == null) {\n      width = height * srcRatio;\n    } else if (height == null) {\n      height = width / srcRatio;\n    }\n\n    // Make sure images aren't upscaled\n    width = Math.min(width, info.srcWidth);\n    height = Math.min(height, info.srcHeight);\n\n    let trgRatio = width / height;\n\n    if (info.srcWidth > width || info.srcHeight > height) {\n      // Image is bigger and needs rescaling\n      if (resizeMethod === \"crop\") {\n        if (srcRatio > trgRatio) {\n          info.srcHeight = file.height;\n          info.srcWidth = info.srcHeight * trgRatio;\n        } else {\n          info.srcWidth = file.width;\n          info.srcHeight = info.srcWidth / trgRatio;\n        }\n      } else if (resizeMethod === \"contain\") {\n        // Method 'contain'\n        if (srcRatio > trgRatio) {\n          height = width / srcRatio;\n        } else {\n          width = height * srcRatio;\n        }\n      } else {\n        throw new Error(`Unknown resizeMethod '${resizeMethod}'`);\n      }\n    }\n\n    info.srcX = (file.width - info.srcWidth) / 2;\n    info.srcY = (file.height - info.srcHeight) / 2;\n\n    info.trgWidth = width;\n    info.trgHeight = height;\n\n    return info;\n  },\n\n  /**\n   * Can be used to transform the file (for example, resize an image if necessary).\n   *\n   * The default implementation uses `resizeWidth` and `resizeHeight` (if provided) and resizes\n   * images according to those dimensions.\n   *\n   * Gets the `file` as the first parameter, and a `done()` function as the second, that needs\n   * to be invoked with the file when the transformation is done.\n   */\n  transformFile(file, done) {\n    if (\n      (this.options.resizeWidth || this.options.resizeHeight) &&\n      file.type.match(/image.*/)\n    ) {\n      return this.resizeImage(\n        file,\n        this.options.resizeWidth,\n        this.options.resizeHeight,\n        this.options.resizeMethod,\n        done\n      );\n    } else {\n      return done(file);\n    }\n  },\n\n  /**\n   * A string that contains the template used for each dropped\n   * file. Change it to fulfill your needs but make sure to properly\n   * provide all elements.\n   *\n   * If you want to use an actual HTML element instead of providing a String\n   * as a config option, you could create a div with the id `tpl`,\n   * put the template inside it and provide the element like this:\n   *\n   *     document\n   *       .querySelector('#tpl')\n   *       .innerHTML\n   *\n   */\n  previewTemplate: defaultPreviewTemplate,\n\n  /*\n   Those functions register themselves to the events on init and handle all\n   the user interface specific stuff. Overwriting them won't break the upload\n   but can break the way it's displayed.\n   You can overwrite them if you don't like the default behavior. If you just\n   want to add an additional event handler, register it on the dropzone object\n   and don't overwrite those options.\n   */\n\n  // Those are self explanatory and simply concern the DragnDrop.\n  drop(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragstart(e) {},\n  dragend(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n  dragenter(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragover(e) {\n    return this.element.classList.add(\"dz-drag-hover\");\n  },\n  dragleave(e) {\n    return this.element.classList.remove(\"dz-drag-hover\");\n  },\n\n  paste(e) {},\n\n  // Called whenever there are no files left in the dropzone anymore, and the\n  // dropzone should be displayed as if in the initial state.\n  reset() {\n    return this.element.classList.remove(\"dz-started\");\n  },\n\n  // Called when a file is added to the queue\n  // Receives `file`\n  addedfile(file) {\n    if (this.element === this.previewsContainer) {\n      this.element.classList.add(\"dz-started\");\n    }\n\n    if (this.previewsContainer && !this.options.disablePreviews) {\n      file.previewElement = Dropzone.createElement(\n        this.options.previewTemplate.trim()\n      );\n      file.previewTemplate = file.previewElement; // Backwards compatibility\n\n      this.previewsContainer.appendChild(file.previewElement);\n      for (var node of file.previewElement.querySelectorAll(\"[data-dz-name]\")) {\n        node.textContent = file.name;\n      }\n      for (node of file.previewElement.querySelectorAll(\"[data-dz-size]\")) {\n        node.innerHTML = this.filesize(file.size);\n      }\n\n      if (this.options.addRemoveLinks) {\n        file._removeLink = Dropzone.createElement(\n          `<a class=\"dz-remove\" href=\"javascript:undefined;\" data-dz-remove>${this.options.dictRemoveFile}</a>`\n        );\n        file.previewElement.appendChild(file._removeLink);\n      }\n\n      let removeFileEvent = (e) => {\n        e.preventDefault();\n        e.stopPropagation();\n        if (file.status === Dropzone.UPLOADING) {\n          return Dropzone.confirm(\n            this.options.dictCancelUploadConfirmation,\n            () => this.removeFile(file)\n          );\n        } else {\n          if (this.options.dictRemoveFileConfirmation) {\n            return Dropzone.confirm(\n              this.options.dictRemoveFileConfirmation,\n              () => this.removeFile(file)\n            );\n          } else {\n            return this.removeFile(file);\n          }\n        }\n      };\n\n      for (let removeLink of file.previewElement.querySelectorAll(\n        \"[data-dz-remove]\"\n      )) {\n        removeLink.addEventListener(\"click\", removeFileEvent);\n      }\n    }\n  },\n\n  // Called whenever a file is removed.\n  removedfile(file) {\n    if (file.previewElement != null && file.previewElement.parentNode != null) {\n      file.previewElement.parentNode.removeChild(file.previewElement);\n    }\n    return this._updateMaxFilesReachedClass();\n  },\n\n  // Called when a thumbnail has been generated\n  // Receives `file` and `dataUrl`\n  thumbnail(file, dataUrl) {\n    if (file.previewElement) {\n      file.previewElement.classList.remove(\"dz-file-preview\");\n      for (let thumbnailElement of file.previewElement.querySelectorAll(\n        \"[data-dz-thumbnail]\"\n      )) {\n        thumbnailElement.alt = file.name;\n        thumbnailElement.src = dataUrl;\n      }\n\n      return setTimeout(\n        () => file.previewElement.classList.add(\"dz-image-preview\"),\n        1\n      );\n    }\n  },\n\n  // Called whenever an error occurs\n  // Receives `file` and `message`\n  error(file, message) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-error\");\n      if (typeof message !== \"string\" && message.error) {\n        message = message.error;\n      }\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-errormessage]\"\n      )) {\n        node.textContent = message;\n      }\n    }\n  },\n\n  errormultiple() {},\n\n  // Called when a file gets processed. Since there is a cue, not all added\n  // files are processed immediately.\n  // Receives `file`\n  processing(file) {\n    if (file.previewElement) {\n      file.previewElement.classList.add(\"dz-processing\");\n      if (file._removeLink) {\n        return (file._removeLink.innerHTML = this.options.dictCancelUpload);\n      }\n    }\n  },\n\n  processingmultiple() {},\n\n  // Called whenever the upload progress gets updated.\n  // Receives `file`, `progress` (percentage 0-100) and `bytesSent`.\n  // To get the total number of bytes of the file, use `file.size`\n  uploadprogress(file, progress, bytesSent) {\n    if (file.previewElement) {\n      for (let node of file.previewElement.querySelectorAll(\n        \"[data-dz-uploadprogress]\"\n      )) {\n        node.nodeName === \"PROGRESS\"\n          ? (node.value = progress)\n          : (node.style.width = `${progress}%`);\n      }\n    }\n  },\n\n  // Called whenever the total upload progress gets updated.\n  // Called with totalUploadProgress (0-100), totalBytes and totalBytesSent\n  totaluploadprogress() {},\n\n  // Called just before the file is sent. Gets the `xhr` object as second\n  // parameter, so you can modify it (for example to add a CSRF token) and a\n  // `formData` object to add additional information.\n  sending() {},\n\n  sendingmultiple() {},\n\n  // When the complete upload is finished and successful\n  // Receives `file`\n  success(file) {\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-success\");\n    }\n  },\n\n  successmultiple() {},\n\n  // When the upload is canceled.\n  canceled(file) {\n    return this.emit(\"error\", file, this.options.dictUploadCanceled);\n  },\n\n  canceledmultiple() {},\n\n  // When the upload is finished, either with success or an error.\n  // Receives `file`\n  complete(file) {\n    if (file._removeLink) {\n      file._removeLink.innerHTML = this.options.dictRemoveFile;\n    }\n    if (file.previewElement) {\n      return file.previewElement.classList.add(\"dz-complete\");\n    }\n  },\n\n  completemultiple() {},\n\n  maxfilesexceeded() {},\n\n  maxfilesreached() {},\n\n  queuecomplete() {},\n\n  addedfiles() {},\n};\n\nexport default defaultOptions;\n", "module.exports = \"966767557311839b\";"], "names": [], "version": 3, "file": "dropzone.js.map"}